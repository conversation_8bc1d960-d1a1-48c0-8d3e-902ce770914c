import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useApp } from '@/context/AppContext';
import { Heart, ShoppingCart, HeartOff } from 'lucide-react-native';
import Header from '@/components/Header';
import Button from '@/components/Button';

export default function FavoritesScreen() {
  const { products, favorites, removeFromFavorites, addToCart } = useApp();

  const favoriteProducts = products.filter(product => favorites.includes(product.id));

  const handleAddToCart = (product: any) => {
    addToCart({
      productId: product.id,
      quantity: 1,
      selectedColor: product.colors[0],
      selectedSize: product.sizes[0],
    });
  };

  if (favoriteProducts.length === 0) {
    return (
      <View style={styles.container}>
        <Header title="Favoris" showBack={true} />
        <View style={styles.emptyContainer}>
          <HeartOff size={64} color="#d1d5db" />
          <Text style={styles.emptyTitle}>Aucun favori</Text>
          <Text style={styles.emptySubtitle}>
            Ajoutez des produits à vos favoris pour les retrouver facilement
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="Favoris"
        showBack={true}
        rightComponent={
          <Text style={styles.itemCount}>{favoriteProducts.length} produit{favoriteProducts.length > 1 ? 's' : ''}</Text>
        }
      />

      <ScrollView style={styles.favoritesList} showsVerticalScrollIndicator={false}>
        {favoriteProducts.map((product) => (
          <View key={product.id} style={styles.favoriteItem}>
            <Image source={{ uri: product.image }} style={styles.productImage} />
            
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{product.name}</Text>
              <Text style={styles.productCode}>Code: {product.code}</Text>
              <Text style={styles.productPrice}>{product.price} DT</Text>
              <Text style={styles.productStock}>Stock: {product.stock}</Text>
              
              <View style={styles.productDetails}>
                <Text style={styles.productDetailLabel}>Couleurs:</Text>
                <Text style={styles.productDetailValue}>{product.colors.join(', ')}</Text>
              </View>
              
              <View style={styles.productDetails}>
                <Text style={styles.productDetailLabel}>Tailles:</Text>
                <Text style={styles.productDetailValue}>{product.sizes.join(', ')}</Text>
              </View>
            </View>

            <View style={styles.itemActions}>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => removeFromFavorites(product.id)}
              >
                <Heart size={20} color="#ef4444" fill="#ef4444" />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.addButton}
                onPress={() => handleAddToCart(product)}
              >
                <ShoppingCart size={16} color="#ffffff" />
                <Text style={styles.addButtonText}>Ajouter</Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Poppins-SemiBold',
    color: '#1f2937',
  },
  itemCount: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#4b5563',
    marginTop: 20,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  favoritesList: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  favoriteItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 16,
  },
  productInfo: {
    flex: 1,
    marginRight: 12,
  },
  productName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1f2937',
    marginBottom: 4,
  },
  productCode: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#3b82f6',
    marginBottom: 4,
  },
  productStock: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#10b981',
    marginBottom: 8,
  },
  productDetails: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  productDetailLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#4b5563',
    width: 60,
  },
  productDetailValue: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    flex: 1,
  },
  itemActions: {
    alignItems: 'center',
    gap: 12,
  },
  removeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#fef2f2',
  },
  addButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  addButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
});