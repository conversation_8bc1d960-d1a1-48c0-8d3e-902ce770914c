import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
  Dimensions,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useApp } from '@/context/AppContext';
import { LinearGradient } from 'expo-linear-gradient';
import Header from '@/components/Header';
import Button from '@/components/Button';

const { width } = Dimensions.get('window');

export default function CustomOrderFormScreen() {
  const { productId } = useLocalSearchParams();
  const { products } = useApp();
  const router = useRouter();
  
  const product = products.find(p => p.id === productId);
  
  const [formData, setFormData] = useState({
    width: '',
    height: '',
    color: '',
    material: '',
    notes: '',
    name: '',
    phone: '',
    address: '',
  });

  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!formData.width || !formData.height || !formData.name || !formData.phone) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    setLoading(true);
    try {
      // Simuler l'envoi de la commande
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Commande envoyée',
        'Votre demande de commande sur mesure a été envoyée avec succès. Nous vous contacterons dans les 24h.',
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          }
        ]
      );
    } catch (error) {
      Alert.alert('Erreur', 'Une erreur est survenue lors de l\'envoi');
    } finally {
      setLoading(false);
    }
  };

  if (!product) {
    return (
      <View style={styles.container}>
        <Header title="Commande sur mesure" showBack={true} />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Produit non trouvé</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header title="Commande sur mesure" showBack={true} />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <LinearGradient
          colors={['#fff5f0', '#ffffff']}
          style={styles.productCard}
        >
          <Text style={styles.productName}>{product.name}</Text>
          <Text style={styles.productCode}>Code: {product.code}</Text>
          <Text style={styles.productPrice}>À partir de {product.price} DT</Text>
        </LinearGradient>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Dimensions *</Text>
          <View style={styles.dimensionsRow}>
            <View style={styles.dimensionInput}>
              <Text style={styles.inputLabel}>Largeur (cm)</Text>
              <TextInput
                style={styles.input}
                value={formData.width}
                onChangeText={(value) => handleInputChange('width', value)}
                placeholder="Ex: 80"
                keyboardType="numeric"
              />
            </View>
            <View style={styles.dimensionInput}>
              <Text style={styles.inputLabel}>Hauteur (cm)</Text>
              <TextInput
                style={styles.input}
                value={formData.height}
                onChangeText={(value) => handleInputChange('height', value)}
                placeholder="Ex: 200"
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Personnalisation</Text>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Couleur souhaitée</Text>
            <TextInput
              style={styles.input}
              value={formData.color}
              onChangeText={(value) => handleInputChange('color', value)}
              placeholder="Ex: Blanc, Noir, Bois naturel..."
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Matériau</Text>
            <TextInput
              style={styles.input}
              value={formData.material}
              onChangeText={(value) => handleInputChange('material', value)}
              placeholder="Ex: Aluminium, PVC, Bois..."
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Notes supplémentaires</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.notes}
              onChangeText={(value) => handleInputChange('notes', value)}
              placeholder="Décrivez vos besoins spécifiques..."
              multiline={true}
              numberOfLines={4}
            />
          </View>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Informations de contact *</Text>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Nom complet</Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="Votre nom complet"
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Téléphone</Text>
            <TextInput
              style={styles.input}
              value={formData.phone}
              onChangeText={(value) => handleInputChange('phone', value)}
              placeholder="Votre numéro de téléphone"
              keyboardType="phone-pad"
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Adresse</Text>
            <TextInput
              style={styles.input}
              value={formData.address}
              onChangeText={(value) => handleInputChange('address', value)}
              placeholder="Votre adresse complète"
            />
          </View>
        </View>

        <View style={styles.submitSection}>
          <Button
            title={loading ? 'Envoi en cours...' : 'Envoyer la demande'}
            onPress={handleSubmit}
            disabled={loading}
            loading={loading}
            variant="primary"
            size="large"
            fullWidth={true}
          />
          <Text style={styles.disclaimer}>
            * Champs obligatoires. Nous vous contacterons dans les 24h pour confirmer votre commande et vous donner un devis personnalisé.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  productCard: {
    padding: 20,
    borderRadius: 16,
    marginTop: 20,
    marginBottom: 30,
    shadowColor: '#ff6b35',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  productName: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#2d3748',
    marginBottom: 4,
  },
  productCode: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 8,
  },
  productPrice: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ff6b35',
  },
  formSection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#2d3748',
    marginBottom: 16,
  },
  dimensionsRow: {
    flexDirection: 'row',
    gap: 16,
  },
  dimensionInput: {
    flex: 1,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderColor: '#f1f5f9',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#2d3748',
    backgroundColor: '#ffffff',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitSection: {
    marginBottom: 40,
  },
  disclaimer: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 18,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
});
