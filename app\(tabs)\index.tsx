import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '@/context/AuthContext';
import { useApp } from '@/context/AppContext';
import { LogOut, ShoppingCart, Heart, Star, Package } from 'lucide-react-native';
import { router } from 'expo-router';
import Logo from '@/components/Logo';
import Button from '@/components/Button';
import IconButton from '@/components/IconButton';

const { width } = Dimensions.get('window');

// Composant Tableau de bord Admin
function AdminDashboard() {
  const { user, logout } = useAuth();
  const { products, orders } = useApp();
  const router = useRouter();

  const handleLogout = () => {
    logout();
    router.replace('/auth');
  };

  const stats = [
    { label: 'Produits', value: products.length, icon: Package, color: '#ff6b35' },
    { label: 'Commandes', value: orders.length, icon: ShoppingCart, color: '#10b981' },
    { label: 'Clients', value: 24, icon: Heart, color: '#f59e0b' },
    { label: 'Revenus', value: '12.5K DT', icon: Star, color: '#ef4444' },
  ];

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#ffffff', '#fff5f0']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Logo size={50} showText={false} variant="primary" />
            <View style={styles.welcomeContainer}>
              <Text style={styles.welcomeText}>Tableau de bord</Text>
              <Text style={styles.userName}>Administrateur</Text>
            </View>
          </View>
          <IconButton onPress={handleLogout} variant="outline" size="medium">
            <LogOut size={20} color="#ff6b35" />
          </IconButton>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          {stats.map((stat, index) => (
            <View key={index} style={styles.statCard}>
              <View style={[styles.statIcon, { backgroundColor: stat.color }]}>
                <stat.icon size={20} color="#ffffff" />
              </View>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Actions rapides</Text>
          <View style={styles.quickActions}>
            <Button
              title="Gestion Produits"
              onPress={() => router.push('/(tabs)/admin')}
              variant="primary"
              size="medium"
              fullWidth={true}
              style={styles.actionButton}
            />
            <Button
              title="Voir Commandes"
              onPress={() => router.push('/(tabs)/search')}
              variant="outline"
              size="medium"
              fullWidth={true}
              style={styles.actionButton}
            />
          </View>
        </View>

        {/* Recent Orders */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Commandes récentes</Text>
          <View style={styles.ordersList}>
            {orders.slice(0, 3).map((order) => (
              <View key={order.id} style={styles.orderCard}>
                <View style={styles.orderInfo}>
                  <Text style={styles.orderId}>#{order.id.slice(-6)}</Text>
                  <Text style={styles.orderDate}>
                    {new Date(order.date).toLocaleDateString('fr-FR')}
                  </Text>
                </View>
                <Text style={styles.orderTotal}>{order.total} DT</Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

export default function HomeScreen() {
  const { user, logout } = useAuth();
  const { products, addToCart, addToFavorites, removeFromFavorites, favorites, orders } = useApp();
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedColor, setSelectedColor] = useState('');
  const [selectedSize, setSelectedSize] = useState('');
  const [showModal, setShowModal] = useState(false);

  const handleLogout = () => {
    logout();
    router.replace('/auth');
  };

  // Si c'est un admin, afficher le tableau de bord admin
  if (user?.isAdmin) {
    return <AdminDashboard />;
  }

  // Sinon, afficher l'interface client

  const categories = [
    { id: 'door', name: 'Portes d\'entrée', icon: '🚪' },
    { id: 'bedroom-door', name: 'Portes chambres', icon: '🪟' },
    { id: 'window', name: 'Fenêtres', icon: '🔳' },
  ];

  const handleAddToCart = (product) => {
    setSelectedProduct(product);
    setSelectedColor(product.colors[0]);
    setSelectedSize(product.sizes[0]);
    setShowModal(true);
  };

  const confirmAddToCart = () => {
    if (selectedProduct) {
      addToCart({
        productId: selectedProduct.id,
        quantity: 1,
        selectedColor,
        selectedSize,
      });
      setShowModal(false);
      setSelectedProduct(null);
    }
  };

  const toggleFavorite = (productId) => {
    if (favorites.includes(productId)) {
      removeFromFavorites(productId);
    } else {
      addToFavorites(productId);
    }
  };

  const handleCustomOrder = (product) => {
    router.push({
      pathname: '/custom-order-form',
      params: { productId: product.id }
    });
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#ffffff', '#fff5f0']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Logo size={50} showText={false} variant="primary" />
            <View style={styles.welcomeContainer}>
              <Text style={styles.welcomeText}>Bienvenue,</Text>
              <Text style={styles.userName}>{user?.name || 'Client'}</Text>
            </View>
          </View>
          <IconButton onPress={handleLogout} variant="outline" size="medium">
            <LogOut size={20} color="#ff6b35" />
          </IconButton>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Catégories</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
            {categories.map((category) => (
              <TouchableOpacity key={category.id} style={styles.categoryCard}>
                <Text style={styles.categoryIcon}>{category.icon}</Text>
                <Text style={styles.categoryName}>{category.name}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Products */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Nos Produits</Text>
          <View style={styles.productsGrid}>
            {products.map((product) => (
              <View key={product.id} style={styles.productCard}>
                <Image source={{ uri: product.image }} style={styles.productImage} />
                
                <TouchableOpacity
                  style={[styles.favoriteButton, favorites.includes(product.id) && styles.favoriteActive]}
                  onPress={() => toggleFavorite(product.id)}
                >
                  <Heart
                    size={20}
                    color={favorites.includes(product.id) ? '#ffffff' : '#6b7280'}
                    fill={favorites.includes(product.id) ? '#ef4444' : 'none'}
                  />
                </TouchableOpacity>

                <View style={styles.productInfo}>
                  <Text style={styles.productName}>{product.name}</Text>
                  <Text style={styles.productCode}>Code: {product.code}</Text>
                  <Text style={styles.productPrice}>{product.price} DT</Text>
                  
                  <View style={styles.productDetails}>
                    <View style={styles.stockContainer}>
                      <Package size={14} color="#10b981" />
                      <Text style={styles.stockText}>Stock: {product.stock}</Text>
                    </View>
                    <View style={styles.ratingContainer}>
                      <Star size={14} color="#fbbf24" fill="#fbbf24" />
                      <Text style={styles.ratingText}>4.8</Text>
                    </View>
                  </View>

                  <View style={styles.colorsContainer}>
                    <Text style={styles.colorsLabel}>Couleurs:</Text>
                    <View style={styles.colorsList}>
                      {product.colors.slice(0, 3).map((color, index) => (
                        <View key={index} style={styles.colorDot} />
                      ))}
                      {product.colors.length > 3 && (
                        <Text style={styles.moreColors}>+{product.colors.length - 3}</Text>
                      )}
                    </View>
                  </View>

                  <View style={styles.sizesContainer}>
                    <Text style={styles.sizesLabel}>Tailles:</Text>
                    <Text style={styles.sizesText}>{product.sizes.join(', ')}</Text>
                  </View>

                  <View style={styles.productButtons}>
                    <Button
                      title="Ajouter"
                      onPress={() => handleAddToCart(product)}
                      variant="primary"
                      size="small"
                      style={styles.addButton}
                    />

                    <Button
                      title="Sur mesure"
                      onPress={() => handleCustomOrder(product)}
                      variant="outline"
                      size="small"
                      style={styles.customButton}
                    />
                  </View>
                </View>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Add to Cart Modal */}
      <Modal
        visible={showModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Ajouter au panier</Text>
            
            {selectedProduct && (
              <>
                <Text style={styles.modalProductName}>{selectedProduct.name}</Text>
                
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Couleur:</Text>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    {selectedProduct.colors.map((color) => (
                      <TouchableOpacity
                        key={color}
                        style={[
                          styles.colorOption,
                          selectedColor === color && styles.selectedColorOption
                        ]}
                        onPress={() => setSelectedColor(color)}
                      >
                        <Text style={[
                          styles.colorOptionText,
                          selectedColor === color && styles.selectedColorOptionText
                        ]}>
                          {color}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>

                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Taille:</Text>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    {selectedProduct.sizes.map((size) => (
                      <TouchableOpacity
                        key={size}
                        style={[
                          styles.sizeOption,
                          selectedSize === size && styles.selectedSizeOption
                        ]}
                        onPress={() => setSelectedSize(size)}
                      >
                        <Text style={[
                          styles.sizeOptionText,
                          selectedSize === size && styles.selectedSizeOptionText
                        ]}>
                          {size}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>

                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowModal(false)}
                  >
                    <Text style={styles.cancelButtonText}>Annuler</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.confirmButton}
                    onPress={confirmAddToCart}
                  >
                    <Text style={styles.confirmButtonText}>Confirmer</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 24,
    paddingHorizontal: 20,
    shadowColor: '#ff6b35',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  welcomeContainer: {
    marginLeft: 12,
  },
  welcomeText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  userName: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#2d3748',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  scrollContent: {
    paddingBottom: 100, // Espace pour la barre de navigation
  },
  section: {
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#1f2937',
    marginBottom: 16,
  },
  categoriesScroll: {
    flexDirection: 'row',
  },
  categoryCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    alignItems: 'center',
    minWidth: 100,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    textAlign: 'center',
  },
  productsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  productCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    marginBottom: 16,
    width: (width - 52) / 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  productImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    resizeMode: 'cover',
  },
  favoriteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 16,
    padding: 6,
  },
  favoriteActive: {
    backgroundColor: '#ef4444',
  },
  productInfo: {
    padding: 12,
  },
  productName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1f2937',
    marginBottom: 4,
  },
  productCode: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#3b82f6',
    marginBottom: 8,
  },
  productDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stockText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#10b981',
    marginLeft: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginLeft: 4,
  },
  colorsContainer: {
    marginBottom: 8,
  },
  colorsLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#4b5563',
    marginBottom: 4,
  },
  colorsList: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#d1d5db',
    marginRight: 4,
  },
  moreColors: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  sizesContainer: {
    marginBottom: 12,
  },
  sizesLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#4b5563',
    marginBottom: 4,
  },
  sizesText: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  productButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  addButton: {
    flex: 1,
  },
  customButton: {
    flex: 1,
  },
  // Styles pour le tableau de bord admin
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 20,
    marginBottom: 30,
  },
  statCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    width: (width - 60) / 2,
    marginBottom: 16,
    shadowColor: '#ff6b35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Poppins-Bold',
    color: '#2d3748',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  quickActions: {
    gap: 12,
  },
  actionButton: {
    marginBottom: 8,
  },
  ordersList: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#ff6b35',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  orderCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#2d3748',
    marginBottom: 4,
  },
  orderDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  orderTotal: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#ff6b35',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 24,
    width: width * 0.9,
    maxHeight: '70%',
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 16,
  },
  modalProductName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalSection: {
    marginBottom: 20,
  },
  modalLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#4b5563',
    marginBottom: 8,
  },
  colorOption: {
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
  },
  selectedColorOption: {
    backgroundColor: '#3b82f6',
  },
  colorOptionText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#374151',
  },
  selectedColorOptionText: {
    color: '#ffffff',
  },
  sizeOption: {
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
  },
  selectedSizeOption: {
    backgroundColor: '#3b82f6',
  },
  sizeOptionText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#374151',
  },
  selectedSizeOptionText: {
    color: '#ffffff',
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6b7280',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: '#3b82f6',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
});