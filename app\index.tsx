import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Image, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useAuth } from '@/context/AuthContext';

const { width, height } = Dimensions.get('window');

export default function WelcomeScreen() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      router.replace('/(tabs)');
    }
  }, [isAuthenticated]);

  useEffect(() => {
    const timer = setTimeout(() => {
      router.replace('/auth');
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <LinearGradient
      colors={['#1e3a8a', '#3b82f6', '#60a5fa']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.container}
    >
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <Image
            source={{ uri: 'https://images.pexels.com/photos/1643389/pexels-photo-1643389.jpeg' }}
            style={styles.logoImage}
          />
          <Text style={styles.logo}>DOORLY</Text>
          <Text style={styles.tagline}>Portes & Fenêtres Premium</Text>
        </View>
        
        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeText}>Bienvenue au</Text>
          <Text style={styles.brandText}>DOORLY</Text>
          <Text style={styles.subtitle}>
            Découvrez notre collection exclusive de portes et fenêtres en aluminium
          </Text>
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  logoImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 20,
    borderWidth: 3,
    borderColor: '#fbbf24',
  },
  logo: {
    fontSize: 32,
    fontFamily: 'Poppins-Bold',
    color: '#ffffff',
    letterSpacing: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  tagline: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#fbbf24',
    textAlign: 'center',
    marginTop: 5,
  },
  welcomeContainer: {
    alignItems: 'center',
    maxWidth: width * 0.9,
  },
  welcomeText: {
    fontSize: 24,
    fontFamily: 'Inter-Regular',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 10,
  },
  brandText: {
    fontSize: 36,
    fontFamily: 'Poppins-Bold',
    color: '#fbbf24',
    textAlign: 'center',
    marginBottom: 20,
    letterSpacing: 1.5,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#e5e7eb',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
});