import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useAuth } from '@/context/AuthContext';
import Logo from '@/components/Logo';

const { width, height } = Dimensions.get('window');

export default function WelcomeScreen() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      router.replace('/home');
    }
  }, [isAuthenticated]);

  useEffect(() => {
    const timer = setTimeout(() => {
      router.replace('/auth');
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <LinearGradient
      colors={['#ffffff', '#fff5f0', '#ffe4d6']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.container}
    >
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <Logo size={120} showText={true} variant="primary" />
          <Text style={styles.tagline}>Portes & Fenêtres Premium</Text>
        </View>

        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeText}>Bienvenue chez</Text>
          <Text style={styles.brandText}>DOORLY</Text>
          <Text style={styles.subtitle}>
            Découvrez notre collection exclusive de portes et fenêtres en aluminium
          </Text>
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  tagline: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ff6b35',
    textAlign: 'center',
    marginTop: 15,
    letterSpacing: 1,
  },
  welcomeContainer: {
    alignItems: 'center',
    maxWidth: width * 0.9,
  },
  welcomeText: {
    fontSize: 24,
    fontFamily: 'Inter-Regular',
    color: '#2d3748',
    textAlign: 'center',
    marginBottom: 10,
  },
  brandText: {
    fontSize: 36,
    fontFamily: 'Poppins-Bold',
    color: '#ff6b35',
    textAlign: 'center',
    marginBottom: 20,
    letterSpacing: 1.5,
    textShadowColor: 'rgba(255, 107, 53, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#4a5568',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
});