import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useApp } from '@/context/AppContext';
import { ArrowLeft, Package, Palette, Ruler, FileText } from 'lucide-react-native';

export default function CustomOrderScreen() {
  const router = useRouter();
  const { productId } = useLocalSearchParams();
  const { products, createOrder } = useApp();
  
  const product = products.find(p => p.id === productId);
  
  const [formData, setFormData] = useState({
    productCode: product?.code || '',
    customSize: '',
    customColor: '',
    notes: '',
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!formData.customSize || !formData.customColor) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    setLoading(true);
    try {
      const orderId = createOrder(
        [{
          productId: product?.id || '',
          quantity: 1,
          selectedColor: formData.customColor,
          selectedSize: formData.customSize,
        }],
        formData
      );

      Alert.alert(
        'Commande sur mesure confirmée',
        `Votre commande personnalisée #${orderId} a été envoyée avec succès!\n\nDétails:\n- Produit: ${product?.name}\n- Code: ${formData.productCode}\n- Taille: ${formData.customSize}\n- Couleur: ${formData.customColor}\n\nNous vous contacterons sous 24h pour confirmer les détails et le prix.`,
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Erreur', 'Une erreur est survenue lors de la commande');
    } finally {
      setLoading(false);
    }
  };

  if (!product) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Produit non trouvé</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color="#1f2937" />
        </TouchableOpacity>
        <Text style={styles.title}>Commande sur mesure</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{product.name}</Text>
          <Text style={styles.productPrice}>À partir de {product.price} DT</Text>
          <Text style={styles.productDescription}>{product.description}</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.formTitle}>Détails de votre commande personnalisée</Text>
          
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Package size={20} color="#3b82f6" />
              <Text style={styles.formLabel}>Code produit</Text>
            </View>
            <TextInput
              style={styles.formInput}
              value={formData.productCode}
              onChangeText={(text) => setFormData({...formData, productCode: text})}
              placeholder="Ex: PE001"
            />
          </View>

          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Ruler size={20} color="#3b82f6" />
              <Text style={styles.formLabel}>Taille personnalisée *</Text>
            </View>
            <TextInput
              style={styles.formInput}
              value={formData.customSize}
              onChangeText={(text) => setFormData({...formData, customSize: text})}
              placeholder="Ex: 95x210cm"
            />
          </View>

          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Palette size={20} color="#3b82f6" />
              <Text style={styles.formLabel}>Couleur personnalisée *</Text>
            </View>
            <TextInput
              style={styles.formInput}
              value={formData.customColor}
              onChangeText={(text) => setFormData({...formData, customColor: text})}
              placeholder="Ex: Bleu marine, RAL 5020"
            />
          </View>

          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={20} color="#3b82f6" />
              <Text style={styles.formLabel}>Notes et spécifications</Text>
            </View>
            <TextInput
              style={[styles.formInput, styles.textArea]}
              value={formData.notes}
              onChangeText={(text) => setFormData({...formData, notes: text})}
              placeholder="Ajoutez des détails supplémentaires, spécifications techniques, ou demandes particulières..."
              multiline
              numberOfLines={4}
            />
          </View>

          <View style={styles.infoBox}>
            <Text style={styles.infoTitle}>Information importante</Text>
            <Text style={styles.infoText}>
              • Les commandes sur mesure nécessitent un délai de fabrication de 2-3 semaines
              • Le prix sera ajusté selon vos spécifications
              • Nous vous contacterons pour confirmer les détails et le devis
              • Un acompte de 30% sera demandé à la confirmation
            </Text>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          disabled={loading}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Envoi en cours...' : 'Envoyer la commande'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#1f2937',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  productInfo: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginVertical: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productName: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#1f2937',
    marginBottom: 8,
  },
  productPrice: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#3b82f6',
    marginBottom: 8,
  },
  productDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    lineHeight: 20,
  },
  formContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  formTitle: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#1f2937',
    marginBottom: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  formLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginLeft: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    backgroundColor: '#ffffff',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  infoBox: {
    backgroundColor: '#eff6ff',
    borderRadius: 8,
    padding: 16,
    marginTop: 20,
  },
  infoTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1e40af',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#3730a3',
    lineHeight: 18,
  },
  footer: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  submitButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  errorText: {
    fontSize: 18,
    fontFamily: 'Inter-Regular',
    color: '#ef4444',
    textAlign: 'center',
    marginTop: 100,
  },
});