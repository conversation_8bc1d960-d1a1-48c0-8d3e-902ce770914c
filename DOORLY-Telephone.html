<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOORLY - Simulateur de Téléphone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            position: relative;
            width: 320px;
            height: 640px;
            background: #1a1a1a;
            border-radius: 35px;
            padding: 8px;
            box-shadow: 
                0 0 0 2px #333,
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 0 8px #1a1a1a;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: #000;
            border-radius: 27px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }

        .app-content {
            height: calc(100% - 44px);
            background: #ffffff;
            overflow-y: auto;
        }

        /* Styles de l'application DOORLY */
        .app-header {
            background: linear-gradient(135deg, #ffffff 0%, #fff5f0 100%);
            padding: 20px;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.1);
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
            margin-bottom: 10px;
        }

        .logo-icon {
            width: 32px;
            height: 38px;
            border: 3px solid #ffffff;
            border-radius: 4px;
            position: relative;
        }

        .logo-icon::after {
            content: '';
            position: absolute;
            right: 15%;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: #ffffff;
            border-radius: 50%;
        }

        .logo-text {
            font-size: 20px;
            font-weight: 800;
            color: #ff6b35;
            letter-spacing: 2px;
            text-align: center;
        }

        .tagline {
            font-size: 12px;
            color: #ff6b35;
            text-align: center;
            margin-top: 5px;
            font-weight: 600;
        }

        .welcome-section {
            text-align: center;
            margin: 20px 0;
        }

        .welcome-text {
            font-size: 18px;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .brand-text {
            font-size: 24px;
            font-weight: 700;
            color: #ff6b35;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
            padding: 0 20px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            padding: 20px;
        }

        .feature-card {
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            color: white;
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
            transition: transform 0.2s;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-2px);
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .feature-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .feature-desc {
            font-size: 11px;
            opacity: 0.9;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: #ffffff;
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            box-shadow: 0 -4px 12px rgba(255, 107, 53, 0.15);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 8px 0;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 11px;
            font-weight: 600;
            transition: color 0.2s;
            cursor: pointer;
        }

        .nav-item.active {
            color: #ff6b35;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .action-buttons {
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .btn {
            padding: 14px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            text-decoration: none;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            color: white;
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #ff6b35;
            border: 2px solid #ff6b35;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .app-content > * {
            animation: fadeIn 0.6s ease-out;
        }

        /* Message de bienvenue */
        .welcome-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff6b35;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            z-index: 1000;
            animation: slideDown 0.5s ease-out;
        }

        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100%); }
            to { transform: translateX(-50%) translateY(0); }
        }

        /* Responsive */
        @media (max-width: 480px) {
            .phone-container {
                width: 300px;
                height: 600px;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-message">
        📱 Application DOORLY - Simulateur de Téléphone
    </div>

    <div class="phone-container">
        <div class="phone-screen">
            <!-- Barre de statut -->
            <div class="status-bar">
                <span>9:41</span>
                <span>📶 📶 📶 🔋</span>
            </div>

            <!-- Contenu de l'application -->
            <div class="app-content">
                <!-- En-tête avec logo -->
                <div class="app-header">
                    <div class="logo-container">
                        <div class="logo">
                            <div class="logo-icon"></div>
                        </div>
                    </div>
                    <div class="logo-text">DOORLY</div>
                    <div class="tagline">Portes & Fenêtres Premium</div>
                </div>

                <!-- Section de bienvenue -->
                <div class="welcome-section">
                    <div class="welcome-text">Bienvenue chez</div>
                    <div class="brand-text">DOORLY</div>
                    <div class="subtitle">
                        Découvrez notre collection exclusive de portes et fenêtres en aluminium
                    </div>
                </div>

                <!-- Grille des fonctionnalités -->
                <div class="features-grid">
                    <div class="feature-card" onclick="showMessage('Catalogue')">
                        <div class="feature-icon">🛍️</div>
                        <div class="feature-title">Catalogue</div>
                        <div class="feature-desc">Découvrez nos produits</div>
                    </div>
                    <div class="feature-card" onclick="showMessage('Recherche')">
                        <div class="feature-icon">🔍</div>
                        <div class="feature-title">Recherche</div>
                        <div class="feature-desc">Trouvez ce que vous cherchez</div>
                    </div>
                    <div class="feature-card" onclick="showMessage('Favoris')">
                        <div class="feature-icon">❤️</div>
                        <div class="feature-title">Favoris</div>
                        <div class="feature-desc">Vos produits préférés</div>
                    </div>
                    <div class="feature-card" onclick="showMessage('Profil')">
                        <div class="feature-icon">👤</div>
                        <div class="feature-title">Profil</div>
                        <div class="feature-desc">Gérez votre compte</div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="showMessage('Catalogue ouvert!')">Voir le catalogue</button>
                    <button class="btn btn-outline" onclick="showMessage('Page de connexion')">Se connecter</button>
                </div>

                <!-- Barre de navigation -->
                <div class="bottom-nav">
                    <div class="nav-item active" onclick="switchTab(this, 'Accueil')">
                        <div class="nav-icon">🏠</div>
                        <div>Accueil</div>
                    </div>
                    <div class="nav-item" onclick="switchTab(this, 'Recherche')">
                        <div class="nav-icon">🔍</div>
                        <div>Recherche</div>
                    </div>
                    <div class="nav-item" onclick="switchTab(this, 'Panier')">
                        <div class="nav-icon">🛒</div>
                        <div>Panier</div>
                    </div>
                    <div class="nav-item" onclick="switchTab(this, 'Favoris')">
                        <div class="nav-icon">❤️</div>
                        <div>Favoris</div>
                    </div>
                    <div class="nav-item" onclick="switchTab(this, 'Profil')">
                        <div class="nav-icon">👤</div>
                        <div>Profil</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentScreen = 'home';

        // Animation au chargement
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page chargée - JavaScript actif !');

            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // Masquer le message de bienvenue après 3 secondes
            setTimeout(() => {
                const welcomeMsg = document.querySelector('.welcome-message');
                if (welcomeMsg) {
                    welcomeMsg.style.opacity = '0';
                    welcomeMsg.style.transform = 'translateX(-50%) translateY(-100%)';
                }
            }, 3000);

            // Ajouter les événements aux boutons
            setupEventListeners();
        });

        function setupEventListeners() {
            // Boutons principaux
            const catalogueBtn = document.querySelector('.btn-primary');
            const loginBtn = document.querySelector('.btn-outline');

            if (catalogueBtn) {
                catalogueBtn.addEventListener('click', () => showCatalogueScreen());
            }

            if (loginBtn) {
                loginBtn.addEventListener('click', () => showLoginScreen());
            }

            // Cartes de fonctionnalités
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach((card, index) => {
                card.addEventListener('click', function() {
                    const title = this.querySelector('.feature-title').textContent;
                    showFeatureScreen(title);
                });
            });

            // Navigation en bas
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    const tabName = this.querySelector('div:last-child').textContent;
                    switchTab(this, tabName);
                });
            });
        }

        function switchTab(element, tabName) {
            // Retirer la classe active de tous les onglets
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Ajouter la classe active à l'onglet cliqué
            element.classList.add('active');

            // Changer l'écran selon l'onglet
            switch(tabName) {
                case 'Accueil':
                    showHomeScreen();
                    break;
                case 'Recherche':
                    showSearchScreen();
                    break;
                case 'Panier':
                    showCartScreen();
                    break;
                case 'Favoris':
                    showFavoritesScreen();
                    break;
                case 'Profil':
                    showProfileScreen();
                    break;
            }

            showMessage(`📱 ${tabName} ouvert`);
        }

        function showMessage(message) {
            // Supprimer les anciens messages
            const oldMessages = document.querySelectorAll('.temp-message');
            oldMessages.forEach(msg => msg.remove());

            // Créer un nouveau message
            const messageDiv = document.createElement('div');
            messageDiv.className = 'temp-message';
            messageDiv.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: #ff6b35;
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                font-weight: 600;
                z-index: 1001;
                box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
                animation: slideDown 0.3s ease-out;
            `;
            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            // Supprimer le message après 2.5 secondes
            setTimeout(() => {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateX(-50%) translateY(-100%)';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 2500);
        }

        // Fonctions pour changer d'écran
        function showHomeScreen() {
            updateAppContent(`
                <div class="app-header">
                    <div class="logo-container">
                        <div class="logo">
                            <div class="logo-icon"></div>
                        </div>
                    </div>
                    <div class="logo-text">DOORLY</div>
                    <div class="tagline">Portes & Fenêtres Premium</div>
                </div>
                <div class="welcome-section">
                    <div class="welcome-text">Bienvenue chez</div>
                    <div class="brand-text">DOORLY</div>
                    <div class="subtitle">Découvrez notre collection exclusive de portes et fenêtres en aluminium</div>
                </div>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🛍️</div>
                        <div class="feature-title">Catalogue</div>
                        <div class="feature-desc">Découvrez nos produits</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔍</div>
                        <div class="feature-title">Recherche</div>
                        <div class="feature-desc">Trouvez ce que vous cherchez</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">❤️</div>
                        <div class="feature-title">Favoris</div>
                        <div class="feature-desc">Vos produits préférés</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">👤</div>
                        <div class="feature-title">Profil</div>
                        <div class="feature-desc">Gérez votre compte</div>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary">Voir le catalogue</button>
                    <button class="btn btn-outline">Se connecter</button>
                </div>
            `);
            setupEventListeners();
        }

        function showCatalogueScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">🛍️ Catalogue</h2>
                </div>
                <div style="padding: 20px;">
                    <div class="product-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div class="product-card" style="background: #fff5f0; padding: 15px; border-radius: 12px; text-align: center;">
                            <div style="font-size: 40px; margin-bottom: 10px;">🚪</div>
                            <h3 style="color: #2d3748; font-size: 14px; margin-bottom: 5px;">Porte Aluminium</h3>
                            <p style="color: #ff6b35; font-weight: 600;">850 DT</p>
                        </div>
                        <div class="product-card" style="background: #fff5f0; padding: 15px; border-radius: 12px; text-align: center;">
                            <div style="font-size: 40px; margin-bottom: 10px;">🪟</div>
                            <h3 style="color: #2d3748; font-size: 14px; margin-bottom: 5px;">Fenêtre PVC</h3>
                            <p style="color: #ff6b35; font-weight: 600;">650 DT</p>
                        </div>
                        <div class="product-card" style="background: #fff5f0; padding: 15px; border-radius: 12px; text-align: center;">
                            <div style="font-size: 40px; margin-bottom: 10px;">🚪</div>
                            <h3 style="color: #2d3748; font-size: 14px; margin-bottom: 5px;">Porte Sécurisée</h3>
                            <p style="color: #ff6b35; font-weight: 600;">1200 DT</p>
                        </div>
                        <div class="product-card" style="background: #fff5f0; padding: 15px; border-radius: 12px; text-align: center;">
                            <div style="font-size: 40px; margin-bottom: 10px;">🪟</div>
                            <h3 style="color: #2d3748; font-size: 14px; margin-bottom: 5px;">Baie Vitrée</h3>
                            <p style="color: #ff6b35; font-weight: 600;">2500 DT</p>
                        </div>
                    </div>
                    <button class="btn btn-outline" style="width: 100%; margin-top: 20px;" onclick="showHomeScreen()">← Retour à l'accueil</button>
                </div>
            `);
        }

        function showLoginScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">🔐 Connexion</h2>
                </div>
                <div style="padding: 20px;">
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; color: #2d3748; font-weight: 600;">Téléphone</label>
                        <input type="tel" placeholder="Votre numéro" style="width: 100%; padding: 12px; border: 2px solid #f1f5f9; border-radius: 8px; font-size: 16px;">
                    </div>
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; color: #2d3748; font-weight: 600;">Mot de passe</label>
                        <input type="password" placeholder="••••••••" style="width: 100%; padding: 12px; border: 2px solid #f1f5f9; border-radius: 8px; font-size: 16px;">
                    </div>
                    <button class="btn btn-primary" style="width: 100%; margin-bottom: 15px;" onclick="showMessage('✅ Connexion réussie!')">Se connecter</button>
                    <button class="btn btn-outline" style="width: 100%;" onclick="showHomeScreen()">← Retour</button>
                    <div style="text-align: center; margin-top: 20px; padding: 15px; background: #fff5f0; border-radius: 8px;">
                        <p style="color: #ff6b35; font-size: 12px; margin: 0;">💡 Admin: 26370541 • Client: autre numéro</p>
                    </div>
                </div>
            `);
        }

        function showSearchScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">🔍 Recherche</h2>
                </div>
                <div style="padding: 20px;">
                    <input type="text" placeholder="Rechercher un produit..." style="width: 100%; padding: 12px; border: 2px solid #f1f5f9; border-radius: 8px; font-size: 16px; margin-bottom: 20px;">
                    <div style="text-align: center; padding: 40px;">
                        <div style="font-size: 48px; margin-bottom: 15px;">🔍</div>
                        <p style="color: #6b7280;">Tapez pour rechercher des produits</p>
                    </div>
                </div>
            `);
        }

        function showCartScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">🛒 Panier</h2>
                </div>
                <div style="padding: 20px; text-align: center;">
                    <div style="font-size: 64px; margin-bottom: 20px;">🛒</div>
                    <h3 style="color: #2d3748; margin-bottom: 10px;">Votre panier est vide</h3>
                    <p style="color: #6b7280; margin-bottom: 20px;">Ajoutez des produits pour commencer</p>
                    <button class="btn btn-primary" onclick="showCatalogueScreen()">Voir le catalogue</button>
                </div>
            `);
        }

        function showFavoritesScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">❤️ Favoris</h2>
                </div>
                <div style="padding: 20px; text-align: center;">
                    <div style="font-size: 64px; margin-bottom: 20px;">❤️</div>
                    <h3 style="color: #2d3748; margin-bottom: 10px;">Aucun favori</h3>
                    <p style="color: #6b7280; margin-bottom: 20px;">Ajoutez des produits à vos favoris</p>
                    <button class="btn btn-primary" onclick="showCatalogueScreen()">Découvrir les produits</button>
                </div>
            `);
        }

        function showProfileScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">👤 Profil</h2>
                </div>
                <div style="padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #ff6b35, #ff8c42); border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center;">
                            <span style="color: white; font-size: 32px;">👤</span>
                        </div>
                        <h3 style="color: #2d3748;">Utilisateur DOORLY</h3>
                        <p style="color: #6b7280;">Client</p>
                    </div>
                    <div style="space-y: 10px;">
                        <div style="padding: 15px; background: #fff5f0; border-radius: 8px; margin-bottom: 10px;">
                            <span style="color: #ff6b35;">📱 Mes commandes</span>
                        </div>
                        <div style="padding: 15px; background: #fff5f0; border-radius: 8px; margin-bottom: 10px;">
                            <span style="color: #ff6b35;">⚙️ Paramètres</span>
                        </div>
                        <div style="padding: 15px; background: #fff5f0; border-radius: 8px; margin-bottom: 10px;">
                            <span style="color: #ff6b35;">📞 Support</span>
                        </div>
                        <button class="btn btn-outline" style="width: 100%; margin-top: 20px;" onclick="showMessage('👋 Déconnexion')">Déconnexion</button>
                    </div>
                </div>
            `);
        }

        function showFeatureScreen(featureName) {
            switch(featureName) {
                case 'Catalogue':
                    showCatalogueScreen();
                    break;
                case 'Recherche':
                    showSearchScreen();
                    break;
                case 'Favoris':
                    showFavoritesScreen();
                    break;
                case 'Profil':
                    showProfileScreen();
                    break;
                default:
                    showMessage(`📱 ${featureName} sélectionné`);
            }
        }

        function updateAppContent(newContent) {
            const appContent = document.querySelector('.app-content');
            if (appContent) {
                appContent.innerHTML = newContent;
                // Réajouter la barre de navigation
                appContent.innerHTML += `
                    <div class="bottom-nav">
                        <div class="nav-item" onclick="switchTab(this, 'Accueil')">
                            <div class="nav-icon">🏠</div>
                            <div>Accueil</div>
                        </div>
                        <div class="nav-item" onclick="switchTab(this, 'Recherche')">
                            <div class="nav-icon">🔍</div>
                            <div>Recherche</div>
                        </div>
                        <div class="nav-item" onclick="switchTab(this, 'Panier')">
                            <div class="nav-icon">🛒</div>
                            <div>Panier</div>
                        </div>
                        <div class="nav-item" onclick="switchTab(this, 'Favoris')">
                            <div class="nav-icon">❤️</div>
                            <div>Favoris</div>
                        </div>
                        <div class="nav-item" onclick="switchTab(this, 'Profil')">
                            <div class="nav-icon">👤</div>
                            <div>Profil</div>
                        </div>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
