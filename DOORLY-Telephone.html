<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOORLY - Simulateur de Téléphone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            position: relative;
            width: 320px;
            height: 640px;
            background: #1a1a1a;
            border-radius: 35px;
            padding: 8px;
            box-shadow: 
                0 0 0 2px #333,
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 0 8px #1a1a1a;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: #000;
            border-radius: 27px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }

        .app-content {
            height: calc(100% - 44px);
            background: #ffffff;
            overflow-y: auto;
        }

        /* Styles de l'application DOORLY */
        .app-header {
            background: linear-gradient(135deg, #ffffff 0%, #fff5f0 100%);
            padding: 20px;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.1);
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
            margin-bottom: 10px;
        }

        .logo-icon {
            width: 32px;
            height: 38px;
            border: 3px solid #ffffff;
            border-radius: 4px;
            position: relative;
        }

        .logo-icon::after {
            content: '';
            position: absolute;
            right: 15%;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: #ffffff;
            border-radius: 50%;
        }

        .logo-text {
            font-size: 20px;
            font-weight: 800;
            color: #ff6b35;
            letter-spacing: 2px;
            text-align: center;
        }

        .tagline {
            font-size: 12px;
            color: #ff6b35;
            text-align: center;
            margin-top: 5px;
            font-weight: 600;
        }

        .welcome-section {
            text-align: center;
            margin: 20px 0;
        }

        .welcome-text {
            font-size: 18px;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .brand-text {
            font-size: 24px;
            font-weight: 700;
            color: #ff6b35;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
            padding: 0 20px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            padding: 20px;
        }

        .feature-card {
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            color: white;
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
            transition: transform 0.2s;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-2px);
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .feature-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .feature-desc {
            font-size: 11px;
            opacity: 0.9;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: #ffffff;
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            box-shadow: 0 -4px 12px rgba(255, 107, 53, 0.15);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 8px 0;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 11px;
            font-weight: 600;
            transition: color 0.2s;
            cursor: pointer;
        }

        .nav-item.active {
            color: #ff6b35;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .action-buttons {
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .btn {
            padding: 14px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            text-decoration: none;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            color: white;
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #ff6b35;
            border: 2px solid #ff6b35;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .app-content > * {
            animation: fadeIn 0.6s ease-out;
        }

        /* Message de bienvenue */
        .welcome-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff6b35;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            z-index: 1000;
            animation: slideDown 0.5s ease-out;
        }

        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100%); }
            to { transform: translateX(-50%) translateY(0); }
        }

        /* Page de bienvenue avec logo 3D */
        .welcome-splash {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .welcome-container {
            text-align: center;
            animation: fadeInUp 1s ease-out;
        }

        .logo-3d-container {
            margin-bottom: 40px;
            perspective: 1000px;
        }

        /* Logo porte 3D comme sur l'image */
        .door-logo {
            position: relative;
            width: 120px;
            height: 140px;
            margin: 0 auto 30px;
            transform-style: preserve-3d;
            animation: logoFloat 3s ease-in-out infinite;
        }

        .door-main {
            position: absolute;
            width: 80px;
            height: 120px;
            background: linear-gradient(145deg, #d4b896, #c4a882);
            border-radius: 8px 8px 4px 4px;
            transform: rotateY(-15deg);
            box-shadow:
                inset 0 2px 4px rgba(255,255,255,0.3),
                inset 0 -2px 4px rgba(0,0,0,0.2),
                4px 8px 16px rgba(0,0,0,0.3);
        }

        .wood-texture {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 1px,
                    rgba(0,0,0,0.05) 1px,
                    rgba(0,0,0,0.05) 2px
                );
            border-radius: 8px 8px 4px 4px;
        }

        .door-handle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: radial-gradient(circle, #8b7355, #6d5a42);
            border-radius: 50%;
            box-shadow:
                inset 0 1px 2px rgba(255,255,255,0.3),
                0 1px 3px rgba(0,0,0,0.4);
        }

        .door-open {
            position: absolute;
            right: -25px;
            top: 5px;
            width: 35px;
            height: 110px;
            background: linear-gradient(145deg, #b8a082, #a89070);
            border-radius: 0 6px 3px 0;
            transform: rotateY(45deg);
            box-shadow:
                inset 0 2px 4px rgba(255,255,255,0.2),
                inset 0 -2px 4px rgba(0,0,0,0.3),
                2px 4px 12px rgba(0,0,0,0.4);
        }

        .wood-texture-open {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 1px,
                    rgba(0,0,0,0.08) 1px,
                    rgba(0,0,0,0.08) 2px
                );
            border-radius: 0 6px 3px 0;
        }

        .door-shadow {
            position: absolute;
            bottom: -10px;
            left: -10px;
            right: -30px;
            height: 20px;
            background: radial-gradient(ellipse, rgba(0,0,0,0.3), transparent);
            transform: rotateX(90deg) translateZ(-10px);
            filter: blur(8px);
        }

        /* Texte DOORLY 3D */
        .text-3d {
            display: flex;
            justify-content: center;
            gap: 2px;
            margin-top: 20px;
        }

        .text-3d span {
            font-family: 'Arial Black', sans-serif;
            font-size: 24px;
            font-weight: 900;
            color: #8b7355;
            text-shadow:
                0 1px 0 #a89070,
                0 2px 0 #9d8065,
                0 3px 0 #92755a,
                0 4px 0 #876a4f,
                0 5px 0 #7c5f44,
                0 6px 1px rgba(0,0,0,0.1),
                0 0 5px rgba(0,0,0,0.1),
                0 1px 3px rgba(0,0,0,0.3),
                0 3px 5px rgba(0,0,0,0.2),
                0 5px 10px rgba(0,0,0,0.25);
            transform: rotateX(15deg);
            display: inline-block;
            animation: letterFloat 2s ease-in-out infinite;
        }

        .letter-d { animation-delay: 0s; }
        .letter-o { animation-delay: 0.1s; }
        .letter-o2 { animation-delay: 0.2s; }
        .letter-r { animation-delay: 0.3s; }
        .letter-l { animation-delay: 0.4s; }
        .letter-y { animation-delay: 0.5s; }

        /* Texte de bienvenue */
        .welcome-text-container {
            margin-bottom: 40px;
        }

        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .welcome-subtitle {
            font-size: 16px;
            color: #8b7355;
            font-weight: 600;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }

        .welcome-description {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
            max-width: 250px;
            margin: 0 auto;
        }

        /* Bouton d'entrée */
        .welcome-actions {
            margin-top: 30px;
        }

        .btn-enter {
            background: linear-gradient(135deg, #8b7355, #a89070);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            box-shadow:
                0 4px 15px rgba(139, 115, 85, 0.4),
                inset 0 1px 0 rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }

        .btn-enter:hover {
            transform: translateY(-2px);
            box-shadow:
                0 6px 20px rgba(139, 115, 85, 0.5),
                inset 0 1px 0 rgba(255,255,255,0.2);
        }

        .btn-enter .arrow {
            transition: transform 0.3s ease;
        }

        .btn-enter:hover .arrow {
            transform: translateX(5px);
        }

        /* Logo mini pour l'app principale */
        .logo-mini {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #8b7355, #a89070);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(139, 115, 85, 0.3);
            margin: 0 auto 10px;
        }

        .door-mini {
            width: 20px;
            height: 24px;
            background: linear-gradient(145deg, #d4b896, #c4a882);
            border-radius: 3px;
            position: relative;
        }

        .wood-texture-mini {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(90deg, transparent, transparent 1px, rgba(0,0,0,0.1) 1px, rgba(0,0,0,0.1) 2px);
            border-radius: 3px;
        }

        .door-handle-mini {
            position: absolute;
            right: 3px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 3px;
            background: #6d5a42;
            border-radius: 50%;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px) rotateY(-5deg); }
            50% { transform: translateY(-10px) rotateY(-5deg); }
        }

        @keyframes letterFloat {
            0%, 100% { transform: rotateX(15deg) translateY(0px); }
            50% { transform: rotateX(15deg) translateY(-3px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Page de connexion */
        .login-screen {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            text-align: center;
            width: 100%;
            max-width: 280px;
            animation: fadeInUp 0.8s ease-out;
        }

        /* Logo en gras en haut */
        .logo-header {
            margin-bottom: 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .logo-mini-login {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #8b7355, #a89070);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
                0 6px 20px rgba(139, 115, 85, 0.4),
                inset 0 2px 4px rgba(255,255,255,0.2);
            animation: logoFloat 3s ease-in-out infinite;
        }

        .door-mini-login {
            width: 24px;
            height: 28px;
            background: linear-gradient(145deg, #d4b896, #c4a882);
            border-radius: 4px;
            position: relative;
            box-shadow: inset 0 1px 2px rgba(255,255,255,0.3);
        }

        .brand-name-bold {
            font-family: 'Arial Black', sans-serif;
            font-size: 32px;
            font-weight: 900;
            color: #8b7355;
            text-shadow:
                0 2px 0 #a89070,
                0 4px 0 #9d8065,
                0 6px 1px rgba(0,0,0,0.1),
                0 0 8px rgba(0,0,0,0.1),
                0 2px 6px rgba(0,0,0,0.3);
            letter-spacing: 2px;
        }

        /* Message de bienvenue */
        .login-welcome {
            margin-bottom: 40px;
        }

        .login-welcome h2 {
            font-size: 22px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .login-welcome p {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
        }

        /* Boutons d'authentification */
        .auth-buttons {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 30px;
        }

        .btn-auth {
            background: linear-gradient(135deg, #8b7355, #a89070);
            color: white;
            border: none;
            padding: 18px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            box-shadow:
                0 4px 15px rgba(139, 115, 85, 0.3),
                inset 0 1px 0 rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-auth::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-auth:hover::before {
            left: 100%;
        }

        .btn-auth:hover {
            transform: translateY(-2px);
            box-shadow:
                0 6px 20px rgba(139, 115, 85, 0.4),
                inset 0 1px 0 rgba(255,255,255,0.2);
        }

        .btn-auth:active {
            transform: translateY(0);
        }

        .btn-inscription {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            box-shadow:
                0 4px 15px rgba(99, 102, 241, 0.3),
                inset 0 1px 0 rgba(255,255,255,0.2);
        }

        .btn-inscription:hover {
            box-shadow:
                0 6px 20px rgba(99, 102, 241, 0.4),
                inset 0 1px 0 rgba(255,255,255,0.2);
        }

        .btn-icon {
            font-size: 18px;
        }

        /* Lien invité */
        .guest-link {
            margin-top: 20px;
        }

        .btn-guest {
            background: transparent;
            color: #6b7280;
            border: none;
            font-size: 14px;
            cursor: pointer;
            text-decoration: underline;
            transition: color 0.3s ease;
        }

        .btn-guest:hover {
            color: #8b7355;
        }

        /* Indicateur de glissement */
        .swipe-indicator {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            animation: swipePulse 2s infinite;
        }

        .swipe-arrow {
            font-size: 24px;
            color: #8b7355;
            margin-bottom: 8px;
            animation: swipeMove 1.5s ease-in-out infinite;
        }

        .swipe-text {
            font-size: 12px;
            color: #6b7280;
            margin: 0;
            font-weight: 500;
        }

        /* Logo plus grand pour la page de connexion */
        .logo-header-large {
            margin-bottom: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .logo-big-login {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #8b7355, #a89070);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
                0 8px 25px rgba(139, 115, 85, 0.5),
                inset 0 3px 6px rgba(255,255,255,0.2);
            animation: logoFloat 3s ease-in-out infinite;
        }

        .door-big-login {
            width: 32px;
            height: 38px;
            background: linear-gradient(145deg, #d4b896, #c4a882);
            border-radius: 5px;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(255,255,255,0.3);
        }

        .wood-texture-big {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(90deg, transparent, transparent 2px, rgba(0,0,0,0.1) 2px, rgba(0,0,0,0.1) 3px);
            border-radius: 5px;
        }

        .door-handle-big {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 4px;
            background: #6d5a42;
            border-radius: 50%;
            box-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .brand-name-extra-bold {
            font-family: 'Arial Black', sans-serif;
            font-size: 42px;
            font-weight: 900;
            color: #8b7355;
            text-shadow:
                0 3px 0 #a89070,
                0 6px 0 #9d8065,
                0 9px 1px rgba(0,0,0,0.1),
                0 0 12px rgba(0,0,0,0.1),
                0 3px 8px rgba(0,0,0,0.3);
            letter-spacing: 3px;
            animation: textGlow 3s ease-in-out infinite;
        }

        /* Boutons plus grands */
        .auth-buttons-large {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 40px;
        }

        .btn-auth-large {
            background: linear-gradient(135deg, #8b7355, #a89070);
            color: white;
            border: none;
            padding: 22px 32px;
            border-radius: 16px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            box-shadow:
                0 6px 20px rgba(139, 115, 85, 0.4),
                inset 0 2px 0 rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-auth-large::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .btn-auth-large:hover::before {
            left: 100%;
        }

        .btn-auth-large:hover {
            transform: translateY(-3px);
            box-shadow:
                0 8px 25px rgba(139, 115, 85, 0.5),
                inset 0 2px 0 rgba(255,255,255,0.2);
        }

        .btn-inscription {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            box-shadow:
                0 6px 20px rgba(99, 102, 241, 0.4),
                inset 0 2px 0 rgba(255,255,255,0.2);
        }

        .btn-inscription:hover {
            box-shadow:
                0 8px 25px rgba(99, 102, 241, 0.5),
                inset 0 2px 0 rgba(255,255,255,0.2);
        }

        .btn-icon-large {
            font-size: 22px;
        }

        /* Animations supplémentaires */
        @keyframes swipePulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        @keyframes swipeMove {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(10px); }
        }

        @keyframes textGlow {
            0%, 100% { text-shadow:
                0 3px 0 #a89070,
                0 6px 0 #9d8065,
                0 9px 1px rgba(0,0,0,0.1),
                0 0 12px rgba(0,0,0,0.1),
                0 3px 8px rgba(0,0,0,0.3); }
            50% { text-shadow:
                0 3px 0 #a89070,
                0 6px 0 #9d8065,
                0 9px 1px rgba(0,0,0,0.1),
                0 0 20px rgba(139, 115, 85, 0.3),
                0 3px 8px rgba(0,0,0,0.3); }
        }

        /* Responsive */
        @media (max-width: 480px) {
            .phone-container {
                width: 300px;
                height: 600px;
            }

            .welcome-title {
                font-size: 24px;
            }

            .door-logo {
                width: 100px;
                height: 120px;
            }

            .text-3d span {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-message">
        📱 Application DOORLY - Simulateur de Téléphone
    </div>

    <div class="phone-container">
        <div class="phone-screen">
            <!-- Barre de statut -->
            <div class="status-bar">
                <span>9:41</span>
                <span>📶 📶 📶 🔋</span>
            </div>

            <!-- Contenu de l'application -->
            <div class="app-content">
                <!-- En-tête avec logo -->
                <div class="app-header">
                    <div class="logo-container">
                        <div class="logo">
                            <div class="logo-icon"></div>
                        </div>
                    </div>
                    <div class="logo-text">DOORLY</div>
                    <div class="tagline">Portes & Fenêtres Premium</div>
                </div>

                <!-- Section de bienvenue -->
                <div class="welcome-section">
                    <div class="welcome-text">Bienvenue chez</div>
                    <div class="brand-text">DOORLY</div>
                    <div class="subtitle">
                        Découvrez notre collection exclusive de portes et fenêtres en aluminium
                    </div>
                </div>

                <!-- Grille des fonctionnalités -->
                <div class="features-grid">
                    <div class="feature-card" onclick="showMessage('Catalogue')">
                        <div class="feature-icon">🛍️</div>
                        <div class="feature-title">Catalogue</div>
                        <div class="feature-desc">Découvrez nos produits</div>
                    </div>
                    <div class="feature-card" onclick="showMessage('Recherche')">
                        <div class="feature-icon">🔍</div>
                        <div class="feature-title">Recherche</div>
                        <div class="feature-desc">Trouvez ce que vous cherchez</div>
                    </div>
                    <div class="feature-card" onclick="showMessage('Favoris')">
                        <div class="feature-icon">❤️</div>
                        <div class="feature-title">Favoris</div>
                        <div class="feature-desc">Vos produits préférés</div>
                    </div>
                    <div class="feature-card" onclick="showMessage('Profil')">
                        <div class="feature-icon">👤</div>
                        <div class="feature-title">Profil</div>
                        <div class="feature-desc">Gérez votre compte</div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="showMessage('Catalogue ouvert!')">Voir le catalogue</button>
                    <button class="btn btn-outline" onclick="showMessage('Page de connexion')">Se connecter</button>
                </div>

                <!-- Barre de navigation -->
                <div class="bottom-nav">
                    <div class="nav-item active" onclick="switchTab(this, 'Accueil')">
                        <div class="nav-icon">🏠</div>
                        <div>Accueil</div>
                    </div>
                    <div class="nav-item" onclick="switchTab(this, 'Recherche')">
                        <div class="nav-icon">🔍</div>
                        <div>Recherche</div>
                    </div>
                    <div class="nav-item" onclick="switchTab(this, 'Panier')">
                        <div class="nav-icon">🛒</div>
                        <div>Panier</div>
                    </div>
                    <div class="nav-item" onclick="switchTab(this, 'Favoris')">
                        <div class="nav-icon">❤️</div>
                        <div>Favoris</div>
                    </div>
                    <div class="nav-item" onclick="switchTab(this, 'Profil')">
                        <div class="nav-icon">👤</div>
                        <div>Profil</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentScreen = 'home';

        // Animation au chargement
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page chargée - JavaScript actif !');

            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // Masquer le message de bienvenue après 3 secondes
            setTimeout(() => {
                const welcomeMsg = document.querySelector('.welcome-message');
                if (welcomeMsg) {
                    welcomeMsg.style.opacity = '0';
                    welcomeMsg.style.transform = 'translateX(-50%) translateY(-100%)';
                }
            }, 3000);

            // Ajouter les événements aux boutons
            setupEventListeners();
        });

        function setupEventListeners() {
            // Boutons principaux
            const catalogueBtn = document.querySelector('.btn-primary');
            const loginBtn = document.querySelector('.btn-outline');

            if (catalogueBtn) {
                catalogueBtn.addEventListener('click', () => showCatalogueScreen());
            }

            if (loginBtn) {
                loginBtn.addEventListener('click', () => showLoginScreen());
            }

            // Cartes de fonctionnalités
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach((card, index) => {
                card.addEventListener('click', function() {
                    const title = this.querySelector('.feature-title').textContent;
                    showFeatureScreen(title);
                });
            });

            // Navigation en bas
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    const tabName = this.querySelector('div:last-child').textContent;
                    switchTab(this, tabName);
                });
            });
        }

        function switchTab(element, tabName) {
            // Retirer la classe active de tous les onglets
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Ajouter la classe active à l'onglet cliqué
            element.classList.add('active');

            // Changer l'écran selon l'onglet
            switch(tabName) {
                case 'Accueil':
                    showHomeScreen();
                    break;
                case 'Recherche':
                    showSearchScreen();
                    break;
                case 'Panier':
                    showCartScreen();
                    break;
                case 'Favoris':
                    showFavoritesScreen();
                    break;
                case 'Profil':
                    showProfileScreen();
                    break;
            }

            showMessage(`📱 ${tabName} ouvert`);
        }

        function showMessage(message) {
            // Supprimer les anciens messages
            const oldMessages = document.querySelectorAll('.temp-message');
            oldMessages.forEach(msg => msg.remove());

            // Créer un nouveau message
            const messageDiv = document.createElement('div');
            messageDiv.className = 'temp-message';
            messageDiv.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: #ff6b35;
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                font-weight: 600;
                z-index: 1001;
                box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
                animation: slideDown 0.3s ease-out;
            `;
            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            // Supprimer le message après 2.5 secondes
            setTimeout(() => {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateX(-50%) translateY(-100%)';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 2500);
        }

        // Fonctions pour changer d'écran
        function showHomeScreen() {
            updateAppContent(`
                <div class="welcome-splash" id="welcomePage">
                    <div class="welcome-container">
                        <!-- Logo DOORLY 3D comme sur l'image -->
                        <div class="logo-3d-container">
                            <div class="door-logo">
                                <!-- Porte principale -->
                                <div class="door-main">
                                    <div class="wood-texture"></div>
                                    <div class="door-handle"></div>
                                </div>
                                <!-- Porte ouverte -->
                                <div class="door-open">
                                    <div class="wood-texture-open"></div>
                                </div>
                                <!-- Ombre -->
                                <div class="door-shadow"></div>
                            </div>

                            <!-- Texte DOORLY 3D -->
                            <div class="text-3d">
                                <span class="letter-d">D</span>
                                <span class="letter-o">O</span>
                                <span class="letter-o2">O</span>
                                <span class="letter-r">R</span>
                                <span class="letter-l">L</span>
                                <span class="letter-y">Y</span>
                            </div>
                        </div>

                        <!-- Texte de bienvenue seulement -->
                        <div class="welcome-text-container">
                            <h1 class="welcome-title">Bienvenue chez DOORLY</h1>
                            <p class="welcome-subtitle">Portes & Fenêtres Premium</p>
                        </div>

                        <!-- Indicateur de glissement -->
                        <div class="swipe-indicator">
                            <div class="swipe-arrow">→</div>
                            <p class="swipe-text">Glissez pour continuer</p>
                        </div>
                    </div>
                </div>
            `);

            // Ajouter les événements de glissement
            setupSwipeEvents();
        }

        function showLoginScreen() {
            updateAppContent(`
                <div class="login-screen">
                    <div class="login-container">
                        <!-- Logo DOORLY très grand et en gras -->
                        <div class="logo-header-large">
                            <div class="logo-big-login">
                                <div class="door-big-login">
                                    <div class="wood-texture-big"></div>
                                    <div class="door-handle-big"></div>
                                </div>
                            </div>
                            <div class="brand-name-extra-bold">DOORLY</div>
                        </div>

                        <!-- Boutons de connexion et inscription seulement -->
                        <div class="auth-buttons-large">
                            <button class="btn-auth-large btn-connexion" onclick="showMainApp()">
                                <span class="btn-icon-large">👤</span>
                                <span>Connexion</span>
                            </button>

                            <button class="btn-auth-large btn-inscription" onclick="showMainApp()">
                                <span class="btn-icon-large">✨</span>
                                <span>Inscription</span>
                            </button>
                        </div>
                    </div>
                </div>
            `);
        }

        // Fonction pour gérer les événements de glissement
        function setupSwipeEvents() {
            const welcomePage = document.getElementById('welcomePage');
            if (!welcomePage) return;

            let startX = 0;
            let startY = 0;
            let endX = 0;
            let endY = 0;

            // Événements tactiles
            welcomePage.addEventListener('touchstart', function(e) {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
            });

            welcomePage.addEventListener('touchend', function(e) {
                endX = e.changedTouches[0].clientX;
                endY = e.changedTouches[0].clientY;
                handleSwipe();
            });

            // Événements souris pour desktop
            welcomePage.addEventListener('mousedown', function(e) {
                startX = e.clientX;
                startY = e.clientY;
            });

            welcomePage.addEventListener('mouseup', function(e) {
                endX = e.clientX;
                endY = e.clientY;
                handleSwipe();
            });

            function handleSwipe() {
                const deltaX = endX - startX;
                const deltaY = endY - startY;
                const minSwipeDistance = 50;

                // Glissement vers la gauche (pour aller à la page suivante)
                if (Math.abs(deltaX) > Math.abs(deltaY) && deltaX < -minSwipeDistance) {
                    showLoginScreen();
                }
            }
        }

        function showMainApp() {
            updateAppContent(`
                <div class="app-header">
                    <div class="logo-container">
                        <div class="logo-mini">
                            <div class="door-mini">
                                <div class="wood-texture-mini"></div>
                                <div class="door-handle-mini"></div>
                            </div>
                        </div>
                    </div>
                    <div class="logo-text">DOORLY</div>
                    <div class="tagline">Portes & Fenêtres Premium</div>
                </div>
                <div class="welcome-section">
                    <div class="welcome-text">Bienvenue chez</div>
                    <div class="brand-text">DOORLY</div>
                    <div class="subtitle">Découvrez notre collection exclusive de portes et fenêtres en aluminium</div>
                </div>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🛍️</div>
                        <div class="feature-title">Catalogue</div>
                        <div class="feature-desc">Découvrez nos produits</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔍</div>
                        <div class="feature-title">Recherche</div>
                        <div class="feature-desc">Trouvez ce que vous cherchez</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">❤️</div>
                        <div class="feature-title">Favoris</div>
                        <div class="feature-desc">Vos produits préférés</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">👤</div>
                        <div class="feature-title">Profil</div>
                        <div class="feature-desc">Gérez votre compte</div>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary">Voir le catalogue</button>
                    <button class="btn btn-outline">Se connecter</button>
                </div>
            `);
            setupEventListeners();
        }

        function showCatalogueScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">🛍️ Catalogue</h2>
                </div>
                <div style="padding: 20px;">
                    <div class="product-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div class="product-card" style="background: #fff5f0; padding: 15px; border-radius: 12px; text-align: center;">
                            <div style="font-size: 40px; margin-bottom: 10px;">🚪</div>
                            <h3 style="color: #2d3748; font-size: 14px; margin-bottom: 5px;">Porte Aluminium</h3>
                            <p style="color: #ff6b35; font-weight: 600;">850 DT</p>
                        </div>
                        <div class="product-card" style="background: #fff5f0; padding: 15px; border-radius: 12px; text-align: center;">
                            <div style="font-size: 40px; margin-bottom: 10px;">🪟</div>
                            <h3 style="color: #2d3748; font-size: 14px; margin-bottom: 5px;">Fenêtre PVC</h3>
                            <p style="color: #ff6b35; font-weight: 600;">650 DT</p>
                        </div>
                        <div class="product-card" style="background: #fff5f0; padding: 15px; border-radius: 12px; text-align: center;">
                            <div style="font-size: 40px; margin-bottom: 10px;">🚪</div>
                            <h3 style="color: #2d3748; font-size: 14px; margin-bottom: 5px;">Porte Sécurisée</h3>
                            <p style="color: #ff6b35; font-weight: 600;">1200 DT</p>
                        </div>
                        <div class="product-card" style="background: #fff5f0; padding: 15px; border-radius: 12px; text-align: center;">
                            <div style="font-size: 40px; margin-bottom: 10px;">🪟</div>
                            <h3 style="color: #2d3748; font-size: 14px; margin-bottom: 5px;">Baie Vitrée</h3>
                            <p style="color: #ff6b35; font-weight: 600;">2500 DT</p>
                        </div>
                    </div>
                    <button class="btn btn-outline" style="width: 100%; margin-top: 20px;" onclick="showHomeScreen()">← Retour à l'accueil</button>
                </div>
            `);
        }

        function showLoginScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">🔐 Connexion</h2>
                </div>
                <div style="padding: 20px;">
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; color: #2d3748; font-weight: 600;">Téléphone</label>
                        <input type="tel" placeholder="Votre numéro" style="width: 100%; padding: 12px; border: 2px solid #f1f5f9; border-radius: 8px; font-size: 16px;">
                    </div>
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; color: #2d3748; font-weight: 600;">Mot de passe</label>
                        <input type="password" placeholder="••••••••" style="width: 100%; padding: 12px; border: 2px solid #f1f5f9; border-radius: 8px; font-size: 16px;">
                    </div>
                    <button class="btn btn-primary" style="width: 100%; margin-bottom: 15px;" onclick="showMessage('✅ Connexion réussie!')">Se connecter</button>
                    <button class="btn btn-outline" style="width: 100%;" onclick="showHomeScreen()">← Retour</button>
                    <div style="text-align: center; margin-top: 20px; padding: 15px; background: #fff5f0; border-radius: 8px;">
                        <p style="color: #ff6b35; font-size: 12px; margin: 0;">💡 Admin: 26370541 • Client: autre numéro</p>
                    </div>
                </div>
            `);
        }

        function showSearchScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">🔍 Recherche</h2>
                </div>
                <div style="padding: 20px;">
                    <input type="text" placeholder="Rechercher un produit..." style="width: 100%; padding: 12px; border: 2px solid #f1f5f9; border-radius: 8px; font-size: 16px; margin-bottom: 20px;">
                    <div style="text-align: center; padding: 40px;">
                        <div style="font-size: 48px; margin-bottom: 15px;">🔍</div>
                        <p style="color: #6b7280;">Tapez pour rechercher des produits</p>
                    </div>
                </div>
            `);
        }

        function showCartScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">🛒 Panier</h2>
                </div>
                <div style="padding: 20px; text-align: center;">
                    <div style="font-size: 64px; margin-bottom: 20px;">🛒</div>
                    <h3 style="color: #2d3748; margin-bottom: 10px;">Votre panier est vide</h3>
                    <p style="color: #6b7280; margin-bottom: 20px;">Ajoutez des produits pour commencer</p>
                    <button class="btn btn-primary" onclick="showCatalogueScreen()">Voir le catalogue</button>
                </div>
            `);
        }

        function showFavoritesScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">❤️ Favoris</h2>
                </div>
                <div style="padding: 20px; text-align: center;">
                    <div style="font-size: 64px; margin-bottom: 20px;">❤️</div>
                    <h3 style="color: #2d3748; margin-bottom: 10px;">Aucun favori</h3>
                    <p style="color: #6b7280; margin-bottom: 20px;">Ajoutez des produits à vos favoris</p>
                    <button class="btn btn-primary" onclick="showCatalogueScreen()">Découvrir les produits</button>
                </div>
            `);
        }

        function showProfileScreen() {
            updateAppContent(`
                <div class="screen-header">
                    <h2 style="color: #ff6b35; text-align: center; padding: 20px; font-size: 24px;">👤 Profil</h2>
                </div>
                <div style="padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #ff6b35, #ff8c42); border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center;">
                            <span style="color: white; font-size: 32px;">👤</span>
                        </div>
                        <h3 style="color: #2d3748;">Utilisateur DOORLY</h3>
                        <p style="color: #6b7280;">Client</p>
                    </div>
                    <div style="space-y: 10px;">
                        <div style="padding: 15px; background: #fff5f0; border-radius: 8px; margin-bottom: 10px;">
                            <span style="color: #ff6b35;">📱 Mes commandes</span>
                        </div>
                        <div style="padding: 15px; background: #fff5f0; border-radius: 8px; margin-bottom: 10px;">
                            <span style="color: #ff6b35;">⚙️ Paramètres</span>
                        </div>
                        <div style="padding: 15px; background: #fff5f0; border-radius: 8px; margin-bottom: 10px;">
                            <span style="color: #ff6b35;">📞 Support</span>
                        </div>
                        <button class="btn btn-outline" style="width: 100%; margin-top: 20px;" onclick="showMessage('👋 Déconnexion')">Déconnexion</button>
                    </div>
                </div>
            `);
        }

        function showFeatureScreen(featureName) {
            switch(featureName) {
                case 'Catalogue':
                    showCatalogueScreen();
                    break;
                case 'Recherche':
                    showSearchScreen();
                    break;
                case 'Favoris':
                    showFavoritesScreen();
                    break;
                case 'Profil':
                    showProfileScreen();
                    break;
                default:
                    showMessage(`📱 ${featureName} sélectionné`);
            }
        }

        function updateAppContent(newContent) {
            const appContent = document.querySelector('.app-content');
            if (appContent) {
                appContent.innerHTML = newContent;
                // Réajouter la barre de navigation
                appContent.innerHTML += `
                    <div class="bottom-nav">
                        <div class="nav-item" onclick="switchTab(this, 'Accueil')">
                            <div class="nav-icon">🏠</div>
                            <div>Accueil</div>
                        </div>
                        <div class="nav-item" onclick="switchTab(this, 'Recherche')">
                            <div class="nav-icon">🔍</div>
                            <div>Recherche</div>
                        </div>
                        <div class="nav-item" onclick="switchTab(this, 'Panier')">
                            <div class="nav-icon">🛒</div>
                            <div>Panier</div>
                        </div>
                        <div class="nav-item" onclick="switchTab(this, 'Favoris')">
                            <div class="nav-icon">❤️</div>
                            <div>Favoris</div>
                        </div>
                        <div class="nav-item" onclick="switchTab(this, 'Profil')">
                            <div class="nav-icon">👤</div>
                            <div>Profil</div>
                        </div>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
