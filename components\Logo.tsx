import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface LogoProps {
  size?: number;
  showText?: boolean;
  variant?: 'primary' | 'white' | 'small';
}

export default function Logo({ size = 80, showText = true, variant = 'primary' }: LogoProps) {
  const logoSize = size;
  const textSize = size * 0.25;
  const iconSize = size * 0.4;

  const getColors = () => {
    switch (variant) {
      case 'white':
        return ['#ffffff', '#f8f9fa'];
      case 'small':
        return ['#ff6b35', '#ff8c42'];
      default:
        return ['#ff6b35', '#ff8c42', '#ffab73'];
    }
  };

  const getTextColor = () => {
    switch (variant) {
      case 'white':
        return '#ff6b35';
      default:
        return '#ffffff';
    }
  };

  return (
    <View style={styles.container}>
      {/* Logo circulaire */}
      <LinearGradient
        colors={getColors()}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={[
          styles.logoCircle,
          {
            width: logoSize,
            height: logoSize,
            borderRadius: logoSize / 2,
          }
        ]}
      >
        <View style={styles.logoContent}>
          {/* Icône de porte stylisée */}
          <View style={[styles.doorIcon, { width: iconSize, height: iconSize * 1.2 }]}>
            <View style={[styles.doorFrame, { borderColor: getTextColor() }]}>
              <View style={[styles.doorHandle, { backgroundColor: getTextColor() }]} />
            </View>
          </View>
        </View>
      </LinearGradient>

      {/* Texte DOORLY */}
      {showText && (
        <Text style={[
          styles.logoText,
          {
            fontSize: textSize,
            color: variant === 'white' ? '#2d3748' : '#ff6b35',
            marginTop: size * 0.1,
          }
        ]}>
          DOORLY
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoCircle: {
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#ff6b35',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 10,
  },
  logoContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  doorIcon: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  doorFrame: {
    width: '100%',
    height: '100%',
    borderWidth: 3,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingRight: '15%',
  },
  doorHandle: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  logoText: {
    fontFamily: 'Poppins-Bold',
    fontWeight: '800',
    letterSpacing: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
});
