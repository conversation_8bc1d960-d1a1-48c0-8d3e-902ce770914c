import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, StatusBar } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Menu } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import Logo from './Logo';
import IconButton from './IconButton';

interface HeaderProps {
  title?: string;
  showBack?: boolean;
  showLogo?: boolean;
  showMenu?: boolean;
  onMenuPress?: () => void;
  rightComponent?: React.ReactNode;
  variant?: 'primary' | 'transparent';
}

export default function Header({
  title,
  showBack = false,
  showLogo = false,
  showMenu = false,
  onMenuPress,
  rightComponent,
  variant = 'primary',
}: HeaderProps) {
  const router = useRouter();

  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace('/(tabs)');
    }
  };

  const getColors = () => {
    switch (variant) {
      case 'transparent':
        return ['rgba(255, 255, 255, 0.95)', 'rgba(255, 245, 240, 0.95)'];
      default:
        return ['#ffffff', '#fff5f0'];
    }
  };

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <LinearGradient
        colors={getColors()}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          {/* Left side */}
          <View style={styles.leftSection}>
            {showBack && (
              <IconButton
                onPress={handleBack}
                variant="outline"
                size="medium"
                style={styles.backButton}
              >
                <ArrowLeft size={20} color="#ff6b35" />
              </IconButton>
            )}
            
            {showMenu && (
              <IconButton
                onPress={onMenuPress}
                variant="outline"
                size="medium"
                style={styles.menuButton}
              >
                <Menu size={20} color="#ff6b35" />
              </IconButton>
            )}

            {showLogo && (
              <Logo size={40} showText={false} variant="primary" />
            )}
          </View>

          {/* Center */}
          <View style={styles.centerSection}>
            {title && (
              <Text style={styles.title}>{title}</Text>
            )}
          </View>

          {/* Right side */}
          <View style={styles.rightSection}>
            {rightComponent}
          </View>
        </View>
      </LinearGradient>
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    shadowColor: '#ff6b35',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 44,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  centerSection: {
    flex: 2,
    alignItems: 'center',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flex: 1,
  },
  backButton: {
    marginRight: 12,
  },
  menuButton: {
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#2d3748',
    textAlign: 'center',
  },
});
