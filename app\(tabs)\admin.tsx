import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
} from 'react-native';
import { useApp } from '@/context/AppContext';
import { LinearGradient } from 'expo-linear-gradient';
import { Plus, CreditCard as Edit, Trash2, Package, ShoppingCart, Users, TrendingUp } from 'lucide-react-native';
import Logo from '@/components/Logo';
import Button from '@/components/Button';
import IconButton from '@/components/IconButton';

export default function AdminScreen() {
  const { products, orders, addProduct, updateProduct, deleteProduct } = useApp();
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [productForm, setProductForm] = useState({
    name: '',
    category: 'door',
    price: '',
    stock: '',
    colors: '',
    sizes: '',
    image: '',
    description: '',
    code: '',
  });

  const resetForm = () => {
    setProductForm({
      name: '',
      category: 'door',
      price: '',
      stock: '',
      colors: '',
      sizes: '',
      image: '',
      description: '',
      code: '',
    });
    setEditingProduct(null);
  };

  const handleAddProduct = () => {
    if (!productForm.name || !productForm.price || !productForm.code) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    const newProduct = {
      ...productForm,
      price: parseFloat(productForm.price),
      stock: parseInt(productForm.stock) || 0,
      colors: productForm.colors.split(',').map(c => c.trim()),
      sizes: productForm.sizes.split(',').map(s => s.trim()),
      image: productForm.image || 'https://images.pexels.com/photos/1643389/pexels-photo-1643389.jpeg',
    };

    addProduct(newProduct);
    setShowAddModal(false);
    resetForm();
    Alert.alert('Succès', 'Produit ajouté avec succès');
  };

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setProductForm({
      name: product.name,
      category: product.category,
      price: product.price.toString(),
      stock: product.stock.toString(),
      colors: product.colors.join(', '),
      sizes: product.sizes.join(', '),
      image: product.image,
      description: product.description,
      code: product.code,
    });
    setShowAddModal(true);
  };

  const handleUpdateProduct = () => {
    if (!productForm.name || !productForm.price || !productForm.code) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    const updatedProduct = {
      ...productForm,
      price: parseFloat(productForm.price),
      stock: parseInt(productForm.stock) || 0,
      colors: productForm.colors.split(',').map(c => c.trim()),
      sizes: productForm.sizes.split(',').map(s => s.trim()),
    };

    updateProduct(editingProduct.id, updatedProduct);
    setShowAddModal(false);
    resetForm();
    Alert.alert('Succès', 'Produit modifié avec succès');
  };

  const handleDeleteProduct = (productId) => {
    Alert.alert(
      'Supprimer le produit',
      'Êtes-vous sûr de vouloir supprimer ce produit ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: () => {
            deleteProduct(productId);
            Alert.alert('Succès', 'Produit supprimé avec succès');
          },
        },
      ]
    );
  };

  const stats = [
    { label: 'Produits', value: products.length, icon: Package, color: '#3b82f6' },
    { label: 'Commandes', value: orders.length, icon: ShoppingCart, color: '#10b981' },
    { label: 'Clients', value: 24, icon: Users, color: '#f59e0b' },
    { label: 'Revenus', value: '12.5K', icon: TrendingUp, color: '#ef4444' },
  ];

  const categories = [
    { id: 'door', name: 'Porte d\'entrée' },
    { id: 'bedroom-door', name: 'Porte chambre' },
    { id: 'window', name: 'Fenêtre' },
  ];

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#ffffff', '#fff5f0']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Logo size={60} showText={false} variant="primary" />
          <View style={styles.titleContainer}>
            <Text style={styles.title}>Administration</Text>
            <Text style={styles.subtitle}>Gestion des produits et commandes</Text>
          </View>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Stats */}
        <View style={styles.statsContainer}>
          {stats.map((stat, index) => (
            <View key={index} style={styles.statCard}>
              <View style={[styles.statIcon, { backgroundColor: stat.color }]}>
                <stat.icon size={20} color="#ffffff" />
              </View>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>

        {/* Products Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Produits</Text>
            <Button
              title="Ajouter"
              onPress={() => setShowAddModal(true)}
              variant="primary"
              size="small"
              style={styles.addButton}
            />
          </View>

          <View style={styles.productsList}>
            {products.map((product) => (
              <View key={product.id} style={styles.productCard}>
                <View style={styles.productInfo}>
                  <Text style={styles.productName}>{product.name}</Text>
                  <Text style={styles.productCode}>Code: {product.code}</Text>
                  <Text style={styles.productPrice}>{product.price} DT</Text>
                  <Text style={styles.productStock}>Stock: {product.stock}</Text>
                </View>
                <View style={styles.productActions}>
                  <IconButton
                    onPress={() => handleEditProduct(product)}
                    variant="outline"
                    size="small"
                    style={styles.editButton}
                  >
                    <Edit size={16} color="#ff6b35" />
                  </IconButton>
                  <IconButton
                    onPress={() => handleDeleteProduct(product.id)}
                    variant="danger"
                    size="small"
                    style={styles.deleteButton}
                  >
                    <Trash2 size={16} color="#ffffff" />
                  </IconButton>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Orders Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Commandes récentes</Text>
          <View style={styles.ordersList}>
            {orders.map((order) => (
              <View key={order.id} style={styles.orderCard}>
                <View style={styles.orderInfo}>
                  <Text style={styles.orderId}>#{order.id.slice(-6)}</Text>
                  <Text style={styles.orderDate}>
                    {new Date(order.date).toLocaleDateString('fr-FR')}
                  </Text>
                  <Text style={styles.orderItems}>{order.items.length} produit(s)</Text>
                </View>
                <View style={styles.orderDetails}>
                  <Text style={styles.orderTotal}>{order.total} DT</Text>
                  <Text style={[
                    styles.orderStatus,
                    { color: order.status === 'pending' ? '#f59e0b' : '#10b981' }
                  ]}>
                    {order.status === 'pending' ? 'En attente' : 'Confirmé'}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Add/Edit Product Modal */}
      <Modal
        visible={showAddModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => {
          setShowAddModal(false);
          resetForm();
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              {editingProduct ? 'Modifier le produit' : 'Ajouter un produit'}
            </Text>
            
            <ScrollView style={styles.modalScroll} showsVerticalScrollIndicator={false}>
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Nom du produit *</Text>
                <TextInput
                  style={styles.formInput}
                  value={productForm.name}
                  onChangeText={(text) => setProductForm({...productForm, name: text})}
                  placeholder="Ex: Porte d'entrée Premium"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Catégorie</Text>
                <View style={styles.categoryButtons}>
                  {categories.map((cat) => (
                    <TouchableOpacity
                      key={cat.id}
                      style={[
                        styles.categoryButton,
                        productForm.category === cat.id && styles.activeCategoryButton
                      ]}
                      onPress={() => setProductForm({...productForm, category: cat.id})}
                    >
                      <Text style={[
                        styles.categoryButtonText,
                        productForm.category === cat.id && styles.activeCategoryButtonText
                      ]}>
                        {cat.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.formRow}>
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Prix (DT) *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={productForm.price}
                    onChangeText={(text) => setProductForm({...productForm, price: text})}
                    placeholder="Ex: 1200"
                    keyboardType="numeric"
                  />
                </View>
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Stock</Text>
                  <TextInput
                    style={styles.formInput}
                    value={productForm.stock}
                    onChangeText={(text) => setProductForm({...productForm, stock: text})}
                    placeholder="Ex: 10"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Code produit *</Text>
                <TextInput
                  style={styles.formInput}
                  value={productForm.code}
                  onChangeText={(text) => setProductForm({...productForm, code: text})}
                  placeholder="Ex: PE001"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Couleurs (séparées par des virgules)</Text>
                <TextInput
                  style={styles.formInput}
                  value={productForm.colors}
                  onChangeText={(text) => setProductForm({...productForm, colors: text})}
                  placeholder="Ex: Blanc, Noir, Gris"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Tailles (séparées par des virgules)</Text>
                <TextInput
                  style={styles.formInput}
                  value={productForm.sizes}
                  onChangeText={(text) => setProductForm({...productForm, sizes: text})}
                  placeholder="Ex: 80x200cm, 90x200cm"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>URL de l'image</Text>
                <TextInput
                  style={styles.formInput}
                  value={productForm.image}
                  onChangeText={(text) => setProductForm({...productForm, image: text})}
                  placeholder="URL de l'image"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Description</Text>
                <TextInput
                  style={[styles.formInput, styles.textArea]}
                  value={productForm.description}
                  onChangeText={(text) => setProductForm({...productForm, description: text})}
                  placeholder="Description du produit"
                  multiline
                  numberOfLines={3}
                />
              </View>
            </ScrollView>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => {
                  setShowAddModal(false);
                  resetForm();
                }}
              >
                <Text style={styles.cancelButtonText}>Annuler</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.saveButton}
                onPress={editingProduct ? handleUpdateProduct : handleAddProduct}
              >
                <Text style={styles.saveButtonText}>
                  {editingProduct ? 'Modifier' : 'Ajouter'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    shadowColor: '#ff6b35',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleContainer: {
    marginLeft: 16,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    color: '#2d3748',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: -20,
    marginBottom: 30,
  },
  statCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    width: '48%',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Poppins-Bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  section: {
    marginBottom: 30,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#1f2937',
  },
  addButton: {
    minWidth: 100,
  },
  productsList: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1f2937',
    marginBottom: 4,
  },
  productCode: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#3b82f6',
    marginBottom: 4,
  },
  productStock: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#10b981',
  },
  productActions: {
    flexDirection: 'row',
    gap: 8,
  },
  editButton: {
    marginRight: 4,
  },
  deleteButton: {
    marginLeft: 4,
  },
  ordersList: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  orderCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1f2937',
    marginBottom: 4,
  },
  orderDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 4,
  },
  orderItems: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  orderDetails: {
    alignItems: 'flex-end',
  },
  orderTotal: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1f2937',
    marginBottom: 4,
  },
  orderStatus: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 24,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalScroll: {
    maxHeight: 400,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    gap: 12,
  },
  formLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  categoryButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryButton: {
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  activeCategoryButton: {
    backgroundColor: '#3b82f6',
  },
  categoryButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#374151',
  },
  activeCategoryButtonText: {
    color: '#ffffff',
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6b7280',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#3b82f6',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
});