import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useAuth } from '@/context/AuthContext';
import { 
  ShoppingBag, 
  Search, 
  Heart, 
  User, 
  Settings, 
  Package,
  BarChart3,
  Users,
  TrendingUp
} from 'lucide-react-native';
import Logo from '@/components/Logo';
import Button from '@/components/Button';
import IconButton from '@/components/IconButton';

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const router = useRouter();
  const { user, logout } = useAuth();

  const clientFeatures = [
    {
      title: 'Catalogue',
      description: 'Découvrez nos produits',
      icon: ShoppingBag,
      route: '/(tabs)',
      color: '#ff6b35',
    },
    {
      title: 'Recherche',
      description: 'Trouvez ce que vous cherchez',
      icon: Search,
      route: '/(tabs)/search',
      color: '#ff8c42',
    },
    {
      title: 'Favoris',
      description: 'Vos produits préférés',
      icon: Heart,
      route: '/(tabs)/favorites',
      color: '#ffab73',
    },
    {
      title: 'Profil',
      description: 'Gérez votre compte',
      icon: User,
      route: '/(tabs)/profile',
      color: '#ff6b35',
    },
  ];

  const adminFeatures = [
    {
      title: 'Tableau de bord',
      description: 'Vue d\'ensemble',
      icon: BarChart3,
      route: '/(tabs)',
      color: '#ff6b35',
    },
    {
      title: 'Gestion Produits',
      description: 'Gérer le catalogue',
      icon: Package,
      route: '/(tabs)/admin',
      color: '#ff8c42',
    },
    {
      title: 'Commandes',
      description: 'Suivi des commandes',
      icon: TrendingUp,
      route: '/(tabs)/search',
      color: '#ffab73',
    },
    {
      title: 'Paramètres',
      description: 'Configuration',
      icon: Settings,
      route: '/(tabs)/profile',
      color: '#ff6b35',
    },
  ];

  const features = user?.isAdmin ? adminFeatures : clientFeatures;

  const handleFeaturePress = (route: string) => {
    router.push(route as any);
  };

  const handleLogout = () => {
    logout();
    router.replace('/auth');
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#ffffff', '#fff5f0', '#ffe4d6']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.logoSection}>
            <Logo size={60} showText={true} variant="primary" />
          </View>
          
          <View style={styles.userSection}>
            <Text style={styles.welcomeText}>
              Bienvenue, {user?.name || (user?.isAdmin ? 'Admin' : 'Client')}
            </Text>
            <Text style={styles.roleText}>
              {user?.isAdmin ? 'Administrateur' : 'Client'}
            </Text>
          </View>

          <IconButton
            onPress={handleLogout}
            variant="outline"
            size="medium"
          >
            <Settings size={20} color="#ff6b35" />
          </IconButton>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.featuresContainer}>
          <Text style={styles.sectionTitle}>
            {user?.isAdmin ? 'Outils d\'administration' : 'Que souhaitez-vous faire ?'}
          </Text>
          
          <View style={styles.featuresGrid}>
            {features.map((feature, index) => (
              <TouchableOpacity
                key={index}
                style={styles.featureCard}
                onPress={() => handleFeaturePress(feature.route)}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={[feature.color, `${feature.color}CC`]}
                  style={styles.featureGradient}
                >
                  <View style={styles.featureIcon}>
                    <feature.icon size={28} color="#ffffff" />
                  </View>
                  <Text style={styles.featureTitle}>{feature.title}</Text>
                  <Text style={styles.featureDescription}>{feature.description}</Text>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Actions rapides</Text>
          
          <Button
            title={user?.isAdmin ? "Ajouter un produit" : "Voir le panier"}
            onPress={() => router.push(user?.isAdmin ? '/(tabs)/admin' : '/(tabs)/cart')}
            variant="primary"
            size="large"
            fullWidth={true}
            style={styles.quickActionButton}
          />
          
          <Button
            title="Déconnexion"
            onPress={handleLogout}
            variant="outline"
            size="large"
            fullWidth={true}
            style={styles.logoutButton}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 24,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  logoSection: {
    flex: 1,
  },
  userSection: {
    flex: 2,
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#2d3748',
    textAlign: 'center',
  },
  roleText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#ff6b35',
    marginTop: 2,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  featuresContainer: {
    marginTop: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#2d3748',
    marginBottom: 16,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featureCard: {
    width: (width - 60) / 2,
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#ff6b35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  featureGradient: {
    padding: 20,
    alignItems: 'center',
    minHeight: 140,
    justifyContent: 'center',
  },
  featureIcon: {
    marginBottom: 12,
  },
  featureTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#ffffff',
    textAlign: 'center',
    opacity: 0.9,
  },
  quickActions: {
    marginBottom: 100,
  },
  quickActionButton: {
    marginBottom: 12,
  },
  logoutButton: {
    marginBottom: 12,
  },
});
