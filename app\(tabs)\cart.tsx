import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { useApp } from '@/context/AppContext';
import { Minus, Plus, Trash2, ShoppingBag, CreditCard } from 'lucide-react-native';

export default function CartScreen() {
  const { cart, products, removeFromCart, updateCartQuantity, createOrder, clearCart } = useApp();
  const [loading, setLoading] = useState(false);

  const cartItems = cart.map(item => {
    const product = products.find(p => p.id === item.productId);
    return {
      ...item,
      product,
      total: (product?.price || 0) * item.quantity,
    };
  });

  const totalPrice = cartItems.reduce((sum, item) => sum + item.total, 0);

  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
    } else {
      updateCartQuantity(productId, newQuantity);
    }
  };

  const handleCheckout = async () => {
    if (cart.length === 0) {
      Alert.alert('Panier vide', 'Votre panier est vide');
      return;
    }

    setLoading(true);
    try {
      const orderId = createOrder(cart);
      clearCart();
      
      Alert.alert(
        'Commande confirmée',
        `Votre commande #${orderId} a été confirmée avec succès!\n\nTotal: ${totalPrice} DT\n\nVous recevrez une notification avec tous les détails.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Erreur', 'Une erreur est survenue lors de la commande');
    } finally {
      setLoading(false);
    }
  };

  const handleReservation = async () => {
    if (cart.length === 0) {
      Alert.alert('Panier vide', 'Votre panier est vide');
      return;
    }

    Alert.alert(
      'Réservation',
      `Confirmer la réservation de ${cart.length} produit(s) pour un total de ${totalPrice} DT?`,
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Réserver',
          onPress: async () => {
            setLoading(true);
            try {
              const orderId = createOrder(cart);
              clearCart();
              
              Alert.alert(
                'Réservation confirmée',
                `Votre réservation #${orderId} a été confirmée!\n\nTotal: ${totalPrice} DT\n\nVous avez 48h pour finaliser votre achat.`,
                [{ text: 'OK' }]
              );
            } catch (error) {
              Alert.alert('Erreur', 'Une erreur est survenue lors de la réservation');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  if (cart.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Panier</Text>
        </View>
        <View style={styles.emptyContainer}>
          <ShoppingBag size={64} color="#d1d5db" />
          <Text style={styles.emptyTitle}>Votre panier est vide</Text>
          <Text style={styles.emptySubtitle}>
            Ajoutez des produits pour commencer vos achats
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Panier</Text>
        <Text style={styles.itemCount}>{cart.length} produit{cart.length > 1 ? 's' : ''}</Text>
      </View>

      <ScrollView style={styles.cartList} showsVerticalScrollIndicator={false}>
        {cartItems.map((item) => (
          <View key={`${item.productId}-${item.selectedColor}-${item.selectedSize}`} style={styles.cartItem}>
            <Image source={{ uri: item.product?.image }} style={styles.productImage} />
            
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{item.product?.name}</Text>
              <Text style={styles.productCode}>Code: {item.product?.code}</Text>
              <Text style={styles.productVariant}>
                {item.selectedColor} • {item.selectedSize}
              </Text>
              <Text style={styles.productPrice}>{item.product?.price} DT</Text>
            </View>

            <View style={styles.quantityContainer}>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => updateQuantity(item.productId, item.quantity - 1)}
              >
                <Minus size={16} color="#6b7280" />
              </TouchableOpacity>
              
              <Text style={styles.quantityText}>{item.quantity}</Text>
              
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => updateQuantity(item.productId, item.quantity + 1)}
              >
                <Plus size={16} color="#6b7280" />
              </TouchableOpacity>
            </View>

            <View style={styles.itemActions}>
              <Text style={styles.itemTotal}>{item.total} DT</Text>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => removeFromCart(item.productId)}
              >
                <Trash2 size={16} color="#ef4444" />
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.totalContainer}>
          <Text style={styles.totalLabel}>Total</Text>
          <Text style={styles.totalPrice}>{totalPrice} DT</Text>
        </View>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.reserveButton}
            onPress={handleReservation}
            disabled={loading}
          >
            <Text style={styles.reserveButtonText}>
              {loading ? 'Chargement...' : 'Réserver'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.checkoutButton}
            onPress={handleCheckout}
            disabled={loading}
          >
            <CreditCard size={20} color="#ffffff" style={styles.buttonIcon} />
            <Text style={styles.checkoutButtonText}>
              {loading ? 'Chargement...' : 'Acheter'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Poppins-SemiBold',
    color: '#1f2937',
  },
  itemCount: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#4b5563',
    marginTop: 20,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  cartList: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  cartItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  productInfo: {
    flex: 1,
    marginRight: 12,
  },
  productName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1f2937',
    marginBottom: 4,
  },
  productCode: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 4,
  },
  productVariant: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#4b5563',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#3b82f6',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    marginRight: 12,
  },
  quantityButton: {
    padding: 8,
  },
  quantityText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    minWidth: 30,
    textAlign: 'center',
  },
  itemActions: {
    alignItems: 'center',
  },
  itemTotal: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1f2937',
    marginBottom: 8,
  },
  removeButton: {
    padding: 4,
  },
  footer: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  totalLabel: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1f2937',
  },
  totalPrice: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    color: '#3b82f6',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  reserveButton: {
    flex: 1,
    backgroundColor: '#fbbf24',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  reserveButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  checkoutButton: {
    flex: 1,
    backgroundColor: '#3b82f6',
    borderRadius: 12,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  checkoutButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
});