import React, { createContext, useContext, useState, useEffect } from 'react';
import { Product, CartItem, Order } from '@/types';

interface AppContextType {
  products: Product[];
  cart: CartItem[];
  orders: Order[];
  favorites: string[];
  searchQuery: string;
  addToCart: (item: CartItem) => void;
  removeFromCart: (productId: string) => void;
  updateCartQuantity: (productId: string, quantity: number) => void;
  addToFavorites: (productId: string) => void;
  removeFromFavorites: (productId: string) => void;
  setSearchQuery: (query: string) => void;
  createOrder: (items: CartItem[], customDetails?: any) => string;
  addProduct: (product: Omit<Product, 'id'>) => void;
  updateProduct: (id: string, product: Partial<Product>) => void;
  deleteProduct: (id: string) => void;
  clearCart: () => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Porte d\'entrée Premium',
    category: 'door',
    price: 1200,
    stock: 15,
    colors: ['Blanc', 'Noir', 'Bois naturel', 'Gris anthracite'],
    sizes: ['80x200cm', '90x200cm', '100x200cm'],
    image: 'https://images.pexels.com/photos/1643389/pexels-photo-1643389.jpeg',
    description: 'Porte d\'entrée en aluminium haute qualité avec isolation thermique.',
    code: 'PE001',
  },
  {
    id: '2',
    name: 'Porte chambre moderne',
    category: 'bedroom-door',
    price: 450,
    stock: 8,
    colors: ['Blanc', 'Gris', 'Bois clair'],
    sizes: ['70x200cm', '80x200cm', '90x200cm'],
    image: 'https://images.pexels.com/photos/1643383/pexels-photo-1643383.jpeg',
    description: 'Porte intérieure élégante pour chambres.',
    code: 'PC001',
  },
  {
    id: '3',
    name: 'Fenêtre aluminium double vitrage',
    category: 'window',
    price: 380,
    stock: 12,
    colors: ['Blanc', 'Gris', 'Noir'],
    sizes: ['100x120cm', '120x140cm', '150x120cm'],
    image: 'https://images.pexels.com/photos/1643384/pexels-photo-1643384.jpeg',
    description: 'Fenêtre en aluminium avec double vitrage pour une isolation optimale.',
    code: 'FA001',
  },
  {
    id: '4',
    name: 'Porte coulissante',
    category: 'door',
    price: 890,
    stock: 6,
    colors: ['Transparent', 'Fumé', 'Blanc opaque'],
    sizes: ['180x200cm', '200x200cm', '240x200cm'],
    image: 'https://images.pexels.com/photos/1643387/pexels-photo-1643387.jpeg',
    description: 'Porte coulissante en aluminium et verre.',
    code: 'PC002',
  },
];

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [products, setProducts] = useState<Product[]>(mockProducts);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [favorites, setFavorites] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  const addToCart = (item: CartItem) => {
    setCart(prev => {
      const existingItem = prev.find(cartItem => 
        cartItem.productId === item.productId && 
        cartItem.selectedColor === item.selectedColor && 
        cartItem.selectedSize === item.selectedSize
      );

      if (existingItem) {
        return prev.map(cartItem =>
          cartItem.productId === item.productId &&
          cartItem.selectedColor === item.selectedColor &&
          cartItem.selectedSize === item.selectedSize
            ? { ...cartItem, quantity: cartItem.quantity + item.quantity }
            : cartItem
        );
      }

      return [...prev, item];
    });
  };

  const removeFromCart = (productId: string) => {
    setCart(prev => prev.filter(item => item.productId !== productId));
  };

  const updateCartQuantity = (productId: string, quantity: number) => {
    setCart(prev => prev.map(item =>
      item.productId === productId ? { ...item, quantity } : item
    ));
  };

  const addToFavorites = (productId: string) => {
    setFavorites(prev => [...prev, productId]);
  };

  const removeFromFavorites = (productId: string) => {
    setFavorites(prev => prev.filter(id => id !== productId));
  };

  const createOrder = (items: CartItem[], customDetails?: any): string => {
    const orderId = `order_${Date.now()}`;
    const total = items.reduce((sum, item) => {
      const product = products.find(p => p.id === item.productId);
      return sum + (product?.price || 0) * item.quantity;
    }, 0);

    const newOrder: Order = {
      id: orderId,
      userId: 'current_user',
      items,
      total,
      status: 'pending',
      date: new Date(),
      isCustomOrder: !!customDetails,
      customDetails,
    };

    setOrders(prev => [...prev, newOrder]);
    return orderId;
  };

  const addProduct = (product: Omit<Product, 'id'>) => {
    const newProduct: Product = {
      ...product,
      id: `product_${Date.now()}`,
    };
    setProducts(prev => [...prev, newProduct]);
  };

  const updateProduct = (id: string, productUpdate: Partial<Product>) => {
    setProducts(prev => prev.map(product =>
      product.id === id ? { ...product, ...productUpdate } : product
    ));
  };

  const deleteProduct = (id: string) => {
    setProducts(prev => prev.filter(product => product.id !== id));
  };

  const clearCart = () => {
    setCart([]);
  };

  return (
    <AppContext.Provider value={{
      products,
      cart,
      orders,
      favorites,
      searchQuery,
      addToCart,
      removeFromCart,
      updateCartQuantity,
      addToFavorites,
      removeFromFavorites,
      setSearchQuery,
      createOrder,
      addProduct,
      updateProduct,
      deleteProduct,
      clearCart,
    }}>
      {children}
    </AppContext.Provider>
  );
}

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};