export interface Product {
  id: string;
  name: string;
  category: 'door' | 'window' | 'bedroom-door';
  price: number;
  stock: number;
  colors: string[];
  sizes: string[];
  image: string;
  description: string;
  code: string;
}

export interface User {
  id: string;
  phone: string;
  isAdmin: boolean;
  name?: string;
}

export interface CartItem {
  productId: string;
  quantity: number;
  selectedColor: string;
  selectedSize: string;
}

export interface Order {
  id: string;
  userId: string;
  items: CartItem[];
  total: number;
  status: 'pending' | 'confirmed' | 'delivered';
  date: Date;
  isCustomOrder: boolean;
  customDetails?: {
    productCode: string;
    customSize: string;
    customColor: string;
    notes: string;
  };
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}