import React from 'react';
import { Tabs } from 'expo-router';
import { useAuth } from '@/context/AuthContext';
import { Chrome as Home, Search, ShoppingCart, Heart, User, Settings, Package, BarChart3 } from 'lucide-react-native';

export default function TabsLayout() {
  const { user } = useAuth();

  // Couleurs du thème orange et blanc
  const theme = {
    primary: '#ff6b35', // Orange
    secondary: '#ffffff', // Blanc
    accent: '#ff8c42', // Orange plus clair
    text: '#2d3748', // Gris foncé
    border: '#e2e8f0', // Gris clair
  };

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: theme.secondary,
          borderTopWidth: 0,
          paddingBottom: 8,
          paddingTop: 8,
          height: 70,
          shadowColor: theme.primary,
          shadowOffset: { width: 0, height: -4 },
          shadowOpacity: 0.15,
          shadowRadius: 12,
          elevation: 12,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
        },
        tabBarActiveTintColor: theme.primary,
        tabBarInactiveTintColor: '#9ca3af',
        tabBarLabelStyle: {
          fontSize: 11,
          fontFamily: 'Inter-SemiBold',
          fontWeight: '600',
          marginTop: 4,
        },
        tabBarIconStyle: {
          marginTop: 4,
        },
        tabBarItemStyle: {
          paddingVertical: 4,
        },
      }}
    >
      {user?.isAdmin ? (
        // Interface Admin
        <>
          <Tabs.Screen
            name="index"
            options={{
              title: 'Tableau de bord',
              tabBarIcon: ({ size, color }) => (
                <BarChart3 size={size} color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="admin"
            options={{
              title: 'Gestion',
              tabBarIcon: ({ size, color }) => (
                <Package size={size} color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="search"
            options={{
              title: 'Commandes',
              tabBarIcon: ({ size, color }) => (
                <ShoppingCart size={size} color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="profile"
            options={{
              title: 'Paramètres',
              tabBarIcon: ({ size, color }) => (
                <Settings size={size} color={color} />
              ),
            }}
          />
        </>
      ) : (
        // Interface Client
        <>
          <Tabs.Screen
            name="index"
            options={{
              title: 'Accueil',
              tabBarIcon: ({ size, color }) => (
                <Home size={size} color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="search"
            options={{
              title: 'Recherche',
              tabBarIcon: ({ size, color }) => (
                <Search size={size} color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="cart"
            options={{
              title: 'Panier',
              tabBarIcon: ({ size, color }) => (
                <ShoppingCart size={size} color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="favorites"
            options={{
              title: 'Favoris',
              tabBarIcon: ({ size, color }) => (
                <Heart size={size} color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="profile"
            options={{
              title: 'Profil',
              tabBarIcon: ({ size, color }) => (
                <User size={size} color={color} />
              ),
            }}
          />
        </>
      )}

      {/* Onglets cachés pour la navigation mais non affichés dans la barre */}
      <Tabs.Screen
        name="admin"
        options={{
          href: user?.isAdmin ? undefined : null, // Masquer pour les clients
        }}
      />
      <Tabs.Screen
        name="cart"
        options={{
          href: user?.isAdmin ? null : undefined, // Masquer pour les admins
        }}
      />
      <Tabs.Screen
        name="favorites"
        options={{
          href: user?.isAdmin ? null : undefined, // Masquer pour les admins
        }}
      />
    </Tabs>
  );
}